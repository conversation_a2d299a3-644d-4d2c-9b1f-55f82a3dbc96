import { supabase } from './supabaseClient';
import type { ServiceResponse } from '../types';

export interface DashboardStats {
  totalStudents: number;
  totalSessions: number;
  totalAttendance: number;
  upcomingSessions: number;
  recentGradings: number;
  activeInstructors: number;
  thisWeekAttendance: number;
  thisMonthRevenue: number;
}

export interface StudentDashboardStats {
  totalSessions: number;
  attendanceRate: number;
  currentBelt: string;
  nextGrading?: string;
  recentAttendance: number;
  fitnessRecords: number;
}

export interface GuardianDashboardStats {
  linkedStudents: number;
  upcomingSessions: number;
  recentProgress: number;
  pendingRequests: number;
}

export interface RecentActivity {
  id: string;
  type: 'session' | 'attendance' | 'grading' | 'student_added';
  title: string;
  description: string;
  date: string;
  user?: string;
}

export const dashboardService = {
  // Get admin/instructor dashboard stats
  getAdminStats: async (): Promise<ServiceResponse<DashboardStats>> => {
    try {
      // Get total students
      const { count: totalStudents } = await supabase
        .from('students')
        .select('*', { count: 'exact', head: true });

      // Get total sessions
      const { count: totalSessions } = await supabase
        .from('sessions')
        .select('*', { count: 'exact', head: true });

      // Get total attendance records
      const { count: totalAttendance } = await supabase
        .from('attendance')
        .select('*', { count: 'exact', head: true });

      // Get upcoming sessions (next 7 days)
      const today = new Date().toISOString().split('T')[0];
      const nextWeek = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      
      const { count: upcomingSessions } = await supabase
        .from('sessions')
        .select('*', { count: 'exact', head: true })
        .gte('date', today)
        .lte('date', nextWeek);

      // Get active instructors
      const { count: activeInstructors } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .eq('role', 'instructor');

      // Get this week's attendance
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      const { count: thisWeekAttendance } = await supabase
        .from('attendance')
        .select('*', { count: 'exact', head: true })
        .gte('recorded_at', weekAgo);

      const stats: DashboardStats = {
        totalStudents: totalStudents || 0,
        totalSessions: totalSessions || 0,
        totalAttendance: totalAttendance || 0,
        upcomingSessions: upcomingSessions || 0,
        recentGradings: 0, // TODO: Implement when grading system is built
        activeInstructors: activeInstructors || 0,
        thisWeekAttendance: thisWeekAttendance || 0,
        thisMonthRevenue: 0, // TODO: Implement when payment system is built
      };

      return { data: stats, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to fetch dashboard stats' };
    }
  },

  // Get student dashboard stats
  getStudentStats: async (studentId: string): Promise<ServiceResponse<StudentDashboardStats>> => {
    try {
      // Get student info
      const { data: student } = await supabase
        .from('students')
        .select('belt_color')
        .eq('id', studentId)
        .single();

      // Get total sessions attended
      const { count: totalSessions } = await supabase
        .from('attendance')
        .select('*', { count: 'exact', head: true })
        .eq('student_id', studentId)
        .in('status', ['attended_paid', 'attended_unpaid']);

      // Get total possible sessions (for attendance rate)
      const { count: totalPossibleSessions } = await supabase
        .from('sessions')
        .select('*', { count: 'exact', head: true })
        .lte('date', new Date().toISOString().split('T')[0]);

      // Get recent attendance (last 30 days)
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      const { count: recentAttendance } = await supabase
        .from('attendance')
        .select('*', { count: 'exact', head: true })
        .eq('student_id', studentId)
        .in('status', ['attended_paid', 'attended_unpaid'])
        .gte('recorded_at', thirtyDaysAgo);

      // Get fitness records count
      const { count: fitnessRecords } = await supabase
        .from('exercise_logs')
        .select('*', { count: 'exact', head: true })
        .eq('student_id', studentId);

      const attendanceRate = totalPossibleSessions ? 
        Math.round(((totalSessions || 0) / totalPossibleSessions) * 100) : 0;

      const stats: StudentDashboardStats = {
        totalSessions: totalSessions || 0,
        attendanceRate,
        currentBelt: student?.belt_color || 'white',
        nextGrading: undefined, // TODO: Implement when grading system is built
        recentAttendance: recentAttendance || 0,
        fitnessRecords: fitnessRecords || 0,
      };

      return { data: stats, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to fetch student stats' };
    }
  },

  // Get guardian dashboard stats
  getGuardianStats: async (guardianId: string): Promise<ServiceResponse<GuardianDashboardStats>> => {
    try {
      // Get linked students
      const { count: linkedStudents } = await supabase
        .from('students')
        .select('*', { count: 'exact', head: true })
        .eq('guardian_id', guardianId);

      // Get upcoming sessions for linked students
      const today = new Date().toISOString().split('T')[0];
      const nextWeek = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      
      const { count: upcomingSessions } = await supabase
        .from('sessions')
        .select('*', { count: 'exact', head: true })
        .gte('date', today)
        .lte('date', nextWeek);

      // Get pending link requests
      const { count: pendingRequests } = await supabase
        .from('account_link_requests')
        .select('*', { count: 'exact', head: true })
        .eq('guardian_id', guardianId)
        .eq('status', 'pending');

      const stats: GuardianDashboardStats = {
        linkedStudents: linkedStudents || 0,
        upcomingSessions: upcomingSessions || 0,
        recentProgress: 0, // TODO: Implement when progress tracking is built
        pendingRequests: pendingRequests || 0,
      };

      return { data: stats, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to fetch guardian stats' };
    }
  },

  // Get recent activity
  getRecentActivity: async (limit: number = 10): Promise<ServiceResponse<RecentActivity[]>> => {
    try {
      const activities: RecentActivity[] = [];

      // Get recent sessions
      const { data: recentSessions } = await supabase
        .from('sessions')
        .select(`
          id, name, date, start_time,
          instructor:instructor_id(first_name, last_name)
        `)
        .order('created_at', { ascending: false })
        .limit(5);

      if (recentSessions) {
        recentSessions.forEach(session => {
          activities.push({
            id: session.id,
            type: 'session',
            title: 'New Session Created',
            description: `${session.name} scheduled for ${new Date(session.date).toLocaleDateString()}`,
            date: session.date,
            user: session.instructor ? `${session.instructor.first_name} ${session.instructor.last_name}` : undefined
          });
        });
      }

      // Get recent students
      const { data: recentStudents } = await supabase
        .from('students')
        .select('id, first_name, last_name, created_at')
        .order('created_at', { ascending: false })
        .limit(5);

      if (recentStudents) {
        recentStudents.forEach(student => {
          activities.push({
            id: student.id,
            type: 'student_added',
            title: 'New Student Added',
            description: `${student.first_name} ${student.last_name} joined`,
            date: student.created_at,
          });
        });
      }

      // Sort by date and limit
      activities.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      
      return { data: activities.slice(0, limit), error: null };
    } catch (error) {
      return { data: [], error: 'Failed to fetch recent activity' };
    }
  },

  // Get upcoming sessions
  getUpcomingSessions: async (limit: number = 5): Promise<ServiceResponse<any[]>> => {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      const { data, error } = await supabase
        .from('sessions')
        .select(`
          *,
          instructor:instructor_id(id, first_name, last_name, role),
          attendance(id)
        `)
        .gte('date', today)
        .order('date', { ascending: true })
        .order('start_time', { ascending: true })
        .limit(limit);

      if (error) {
        return { data: [], error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      return { data: [], error: 'Failed to fetch upcoming sessions' };
    }
  }
};
