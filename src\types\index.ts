// Database entity types based on the schema

export interface Profile {
  id: string;
  first_name: string;
  last_name: string;
  date_of_birth?: string;
  phone?: string;
  address?: any; // JSONB field
  avatar_url?: string;
  role: 'admin' | 'instructor' | 'student' | 'guardian';
  created_at?: string;
  updated_at?: string;
}

export interface Student {
  id: string;
  user_id?: string;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  address?: any; // JSONB field
  phone?: string;
  emergency_contact?: any; // JSONB field
  belt_color: string;
  last_graded?: string;
  insurance_expiry?: string;
  licence_number?: string;
  guardian_id?: string;
  medical_conditions?: any[]; // JSONB array
  consent_social_media: boolean;
  consent_assumption_risk: boolean;
  active: boolean;
  created_at?: string;
  updated_at?: string;
  // Joined fields
  guardian?: Profile;
  user?: Profile;
}

export interface AccountLinkRequest {
  id: string;
  student_id: string;
  user_id: string;
  relationship_type: 'self' | 'guardian';
  status: 'pending' | 'approved' | 'rejected';
  requested_by: string;
  reviewed_by?: string;
  request_message?: string;
  review_notes?: string;
  requested_at?: string;
  reviewed_at?: string;
  // Joined fields
  student?: Student;
  requester?: Profile;
  reviewer?: Profile;
}

export interface Session {
  id: string;
  name: string;
  date: string;
  start_time: string;
  end_time: string;
  instructor_id: string;
  max_capacity?: number;
  session_type?: string;
  created_at?: string;
  // Joined fields
  instructor?: Profile;
  attendance?: Array<{ id: string }>;
}

export interface Attendance {
  id: string;
  session_id: string;
  student_id: string;
  status: 'attended_paid' | 'attended_unpaid' | 'absent';
  recorded_by: string;
  recorded_at?: string;
  // Joined fields
  session?: Session;
  student?: Student;
  recorder?: Profile;
}

export interface Syllabus {
  id: string;
  belt_color: string;
  category: string;
  skill_name: string;
  description?: string;
  order_index: number;
}

export interface Grading {
  id: string;
  student_id: string;
  syllabus_id: string;
  result: 'pass' | 'fail' | 'not_assessed';
  graded_by: string;
  graded_at?: string;
  notes?: string;
  // Joined fields
  student?: Student;
  syllabus?: Syllabus;
  grader?: Profile;
}

export interface Exercise {
  id: string;
  name: string;
  description?: string;
  measurement_type: 'count' | 'time' | 'weight' | 'distance';
  created_by: string;
  created_at?: string;
  // Joined fields
  creator?: Profile;
}

export interface FitnessRecord {
  id: string;
  student_id: string;
  exercise_id: string;
  value: number;
  recorded_by: string;
  verified_by?: string;
  recorded_at?: string;
  notes?: string;
  // Joined fields
  student?: Student;
  exercise?: Exercise;
  recorder?: Profile;
  verifier?: Profile;
}

// Form types for creating/updating entities
export interface CreateStudentForm {
  first_name: string;
  last_name: string;
  date_of_birth: string;
  address?: any;
  phone?: string;
  emergency_contact?: any;
  belt_color: string;
  insurance_expiry?: string;
  licence_number?: string;
  guardian_id?: string;
  medical_conditions?: any[];
  consent_social_media: boolean;
  consent_assumption_risk: boolean;
}

export interface CreateSessionForm {
  name: string;
  date: string;
  start_time: string;
  end_time: string;
  instructor_id: string;
  max_capacity?: number;
  session_type?: string;
}

export interface CreateExerciseForm {
  name: string;
  description?: string;
  measurement_type: 'count' | 'time' | 'weight' | 'distance';
}

export interface RecordAttendanceForm {
  session_id: string;
  student_id: string;
  status: 'attended_paid' | 'attended_unpaid' | 'absent';
}

export interface RecordGradingForm {
  student_id: string;
  syllabus_id: string;
  result: 'pass' | 'fail' | 'not_assessed';
  notes?: string;
}

export interface RecordFitnessForm {
  student_id: string;
  exercise_id: string;
  value: number;
  notes?: string;
}

// API response types
export interface ApiResponse<T> {
  data: T | null;
  error: string | null;
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  error: string | null;
}

// Filter and search types
export interface StudentFilters {
  belt_color?: string;
  active?: boolean;
  guardian_id?: string;
  search?: string;
}

export interface SessionFilters {
  instructor_id?: string;
  date_from?: string;
  date_to?: string;
  session_type?: string;
}

export interface AttendanceFilters {
  session_id?: string;
  student_id?: string;
  status?: 'attended_paid' | 'attended_unpaid' | 'absent';
  date_from?: string;
  date_to?: string;
}

// Belt colors constant
export const BELT_COLORS = [
  'white',
  'yellow',
  'orange',
  'green',
  'blue',
  'purple',
  'brown',
  'black'
] as const;

export type BeltColor = typeof BELT_COLORS[number];

// Session types constant
export const SESSION_TYPES = [
  'training',
  'grading',
  'competition',
  'seminar',
  'private'
] as const;

export type SessionType = typeof SESSION_TYPES[number];
