import React from 'react';
import { Activity, Target, TrendingUp, Timer } from 'lucide-react';

export const FitnessPage: React.FC = () => {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 flex items-center">
          <Activity className="h-8 w-8 mr-3 text-indigo-600" />
          Fitness Tracking
        </h1>
        <p className="mt-2 text-gray-600">Record and track fitness measurements and progress</p>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Target className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Exercises</p>
              <p className="text-2xl font-bold text-gray-900">--</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Activity className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Records This Week</p>
              <p className="text-2xl font-bold text-gray-900">--</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <TrendingUp className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Improvements</p>
              <p className="text-2xl font-bold text-gray-900">--</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Timer className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending Verification</p>
              <p className="text-2xl font-bold text-gray-900">--</p>
            </div>
          </div>
        </div>
      </div>

      {/* Coming Soon Content */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 text-center">
          <Activity className="mx-auto h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Fitness Tracking Coming Soon</h3>
          <p className="text-gray-500 mb-6">
            This feature will include:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left max-w-2xl mx-auto">
            <div className="space-y-2">
              <div className="flex items-center text-sm text-gray-600">
                <div className="w-2 h-2 bg-indigo-600 rounded-full mr-3"></div>
                Exercise library management
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <div className="w-2 h-2 bg-indigo-600 rounded-full mr-3"></div>
                Fitness record tracking (count, time, weight, distance)
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <div className="w-2 h-2 bg-indigo-600 rounded-full mr-3"></div>
                Progress charts and analytics
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center text-sm text-gray-600">
                <div className="w-2 h-2 bg-indigo-600 rounded-full mr-3"></div>
                Personal best tracking
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <div className="w-2 h-2 bg-indigo-600 rounded-full mr-3"></div>
                Instructor verification system
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <div className="w-2 h-2 bg-indigo-600 rounded-full mr-3"></div>
                Goal setting and achievement tracking
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
