import React, { useState, useEffect } from 'react';
import { User, Heart, Save, Edit, AlertCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { MedicalConditionsSelector } from './MedicalConditionsSelector';
import { studentService } from '../services';
import { useAuth } from '../contexts/AuthContext';

interface LinkedStudent {
  id: string;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  belt_color: string;
  phone?: string;
  consent_social_media: boolean;
  consent_assumption_risk: boolean;
  medical_conditions: any[]; // Changed from string[] to any[] to handle both formats
  address: any;
  emergency_contact: any;
}

interface StudentUpdateForm {
  phone: string;
  consent_social_media: boolean;
  consent_assumption_risk: boolean;
  emergency_contact_name: string;
  emergency_contact_phone: string;
  emergency_contact_relationship: string;
  address_street: string;
  address_city: string;
  address_state: string;
  address_zip: string;
}

export const GuardianStudentManager: React.FC = () => {
  const { state } = useAuth();
  const { profile } = state;
  const [linkedStudents, setLinkedStudents] = useState<LinkedStudent[]>([]);
  const [selectedStudent, setSelectedStudent] = useState<LinkedStudent | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [medicalConditions, setMedicalConditions] = useState<Array<{
    condition_id: string;
    condition_name: string;
    notes?: string;
    severity?: string;
    medication?: string;
  }>>([]);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<StudentUpdateForm>();

  useEffect(() => {
    loadLinkedStudents();
  }, [profile]);

  const loadLinkedStudents = async () => {
    if (!profile) return;
    
    setLoading(true);
    setError(null);

    try {
      // Get students where this user is the guardian
      const result = await studentService.getStudents({ guardian_id: profile.id });
      if (result.error) {
        setError(result.error);
      } else {
        setLinkedStudents(result.data);
      }
    } catch (err) {
      setError('Failed to load linked students');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectStudent = (student: LinkedStudent) => {
    setSelectedStudent(student);
    setError(null);
    setSuccess(null);

    // Initialize form with student data
    reset({
      phone: student.phone || '',
      consent_social_media: student.consent_social_media,
      consent_assumption_risk: student.consent_assumption_risk,
      emergency_contact_name: student.emergency_contact?.name || '',
      emergency_contact_phone: student.emergency_contact?.phone || '',
      emergency_contact_relationship: student.emergency_contact?.relationship || '',
      address_street: student.address?.street || '',
      address_city: student.address?.city || '',
      address_state: student.address?.state || '',
      address_zip: student.address?.zip || ''
    });

    // Initialize medical conditions - handle both string and object formats
    const conditions = student.medical_conditions?.map((condition, index) => {
      if (typeof condition === 'string') {
        // Legacy string format
        return {
          condition_id: `existing_${index}`,
          condition_name: condition,
          notes: '',
          severity: 'mild',
          medication: ''
        };
      } else if (condition && typeof condition === 'object') {
        // New object format
        return {
          condition_id: condition.condition_id || `existing_${index}`,
          condition_name: condition.condition_name || condition.name || 'Unknown Condition',
          notes: condition.notes || '',
          severity: condition.severity || 'mild',
          medication: condition.medication || ''
        };
      } else {
        // Fallback for unexpected format
        return {
          condition_id: `existing_${index}`,
          condition_name: 'Unknown Condition',
          notes: '',
          severity: 'mild',
          medication: ''
        };
      }
    }) || [];
    setMedicalConditions(conditions);
  };

  const onSubmit = async (data: StudentUpdateForm) => {
    if (!selectedStudent) return;

    setSaving(true);
    setError(null);
    setSuccess(null);

    console.log('Submitting student update...', data);

    try {
      const updateData = {
        phone: data.phone || undefined,
        consent_social_media: data.consent_social_media,
        consent_assumption_risk: data.consent_assumption_risk,
        medical_conditions: medicalConditions, // Save as objects instead of just strings
        emergency_contact: {
          name: data.emergency_contact_name,
          phone: data.emergency_contact_phone,
          relationship: data.emergency_contact_relationship
        },
        address: {
          street: data.address_street,
          city: data.address_city,
          state: data.address_state,
          zip: data.address_zip
        }
      };

      const result = await studentService.updateStudent(selectedStudent.id, updateData);
      if (result.error) {
        setError(result.error);
      } else {
        setSuccess('Student information updated successfully!');
        // Reload students to get updated data
        await loadLinkedStudents();
        // Update selected student with new data
        const updatedStudent = result.data;
        if (updatedStudent) {
          // Convert Student type to LinkedStudent type for compatibility
          const linkedStudent: LinkedStudent = {
            ...updatedStudent,
            medical_conditions: updatedStudent.medical_conditions || []
          };
          setSelectedStudent(linkedStudent);
        }
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      }
    } catch (err) {
      setError('Failed to update student information');
    } finally {
      setSaving(false);
    }
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return age - 1;
    }
    return age;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (linkedStudents.length === 0) {
    return (
      <div className="text-center py-12">
        <User className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No linked students</h3>
        <p className="mt-1 text-sm text-gray-500">
          You don't have any linked student accounts yet. Use the account linking feature to connect to your child's student record.
        </p>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Student Selection */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">My Students</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {linkedStudents.map((student) => (
            <div
              key={student.id}
              onClick={() => handleSelectStudent(student)}
              className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                selectedStudent?.id === student.id
                  ? 'border-indigo-500 bg-indigo-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                    <span className="text-sm font-medium text-indigo-600">
                      {student.first_name[0]}{student.last_name[0]}
                    </span>
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {student.first_name} {student.last_name}
                  </p>
                  <p className="text-sm text-gray-500">
                    Age: {calculateAge(student.date_of_birth)} • {student.belt_color.charAt(0).toUpperCase() + student.belt_color.slice(1)} Belt
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Student Update Form */}
      {selectedStudent && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <Edit className="h-6 w-6 text-indigo-600 mr-3" />
              Update {selectedStudent.first_name}'s Information
            </h2>
          </div>

          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                <span className="text-sm text-red-600">{error}</span>
              </div>
            </div>
          )}

          {success && (
            <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
              <div className="text-sm text-green-600">{success}</div>
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            {/* Contact Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    {...register('phone')}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Student's phone number"
                  />
                </div>
              </div>
            </div>

            {/* Emergency Contact */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Emergency Contact</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Contact Name
                  </label>
                  <input
                    type="text"
                    {...register('emergency_contact_name')}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Contact Phone
                  </label>
                  <input
                    type="tel"
                    {...register('emergency_contact_phone')}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Relationship
                  </label>
                  <input
                    type="text"
                    {...register('emergency_contact_relationship')}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="e.g., Parent, Guardian"
                  />
                </div>
              </div>
            </div>

            {/* Address */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Address</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Street Address
                  </label>
                  <input
                    type="text"
                    {...register('address_street')}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      City
                    </label>
                    <input
                      type="text"
                      {...register('address_city')}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      State
                    </label>
                    <input
                      type="text"
                      {...register('address_state')}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      ZIP Code
                    </label>
                    <input
                      type="text"
                      {...register('address_zip')}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Consent */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 flex items-center mb-4">
                <Heart className="h-5 w-5 mr-2 text-indigo-600" />
                Consent & Permissions
              </h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    {...register('consent_social_media')}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">
                    Consent to social media usage (photos/videos for school promotion)
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    {...register('consent_assumption_risk')}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">
                    Assumption of risk consent (understanding of martial arts training risks)
                  </label>
                </div>
              </div>
            </div>

            {/* Medical Conditions */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Medical Conditions</h3>
              <MedicalConditionsSelector
                selectedConditions={medicalConditions}
                onChange={setMedicalConditions}
              />
            </div>

            {/* Submit Button */}
            <div className="flex justify-end pt-6 border-t border-gray-200">
              <button
                type="submit"
                disabled={saving}
                className="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {saving ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                {saving ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};
