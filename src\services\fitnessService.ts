import { supabase } from './supabaseClient';
import type { 
  Exercise, 
  FitnessRecord,
  CreateExerciseForm,
  RecordFitnessForm,
  ApiResponse, 
  PaginatedResponse 
} from '../types';

export const fitnessService = {
  // Get all exercises
  async getExercises(): Promise<PaginatedResponse<Exercise>> {
    try {
      const { data, error, count } = await supabase
        .from('exercises')
        .select(`
          *,
          creator:created_by(id, first_name, last_name, role)
        `)
        .order('name');

      if (error) {
        return { data: [], count: 0, error: error.message };
      }

      return { data: data || [], count: count || 0, error: null };
    } catch (error) {
      return { data: [], count: 0, error: 'Failed to fetch exercises' };
    }
  },

  // Create a new exercise
  async createExercise(exerciseData: CreateExerciseForm, createdBy: string): Promise<ApiResponse<Exercise>> {
    try {
      const { data, error } = await supabase
        .from('exercises')
        .insert([{
          ...exerciseData,
          created_by: createdBy
        }])
        .select(`
          *,
          creator:created_by(id, first_name, last_name, role)
        `)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to create exercise' };
    }
  },

  // Get fitness records with optional filtering
  async getFitnessRecords(studentId?: string, exerciseId?: string): Promise<PaginatedResponse<FitnessRecord>> {
    try {
      let query = supabase
        .from('fitness_records')
        .select(`
          *,
          student:student_id(id, first_name, last_name, belt_color),
          exercise:exercise_id(id, name, measurement_type),
          recorder:recorded_by(id, first_name, last_name, role),
          verifier:verified_by(id, first_name, last_name, role)
        `)
        .order('recorded_at', { ascending: false });

      if (studentId) {
        query = query.eq('student_id', studentId);
      }
      if (exerciseId) {
        query = query.eq('exercise_id', exerciseId);
      }

      const { data, error, count } = await query;

      if (error) {
        return { data: [], count: 0, error: error.message };
      }

      return { data: data || [], count: count || 0, error: null };
    } catch (error) {
      return { data: [], count: 0, error: 'Failed to fetch fitness records' };
    }
  },

  // Record a fitness measurement
  async recordFitness(fitnessData: RecordFitnessForm, recordedBy: string): Promise<ApiResponse<FitnessRecord>> {
    try {
      const { data, error } = await supabase
        .from('fitness_records')
        .insert([{
          ...fitnessData,
          recorded_by: recordedBy
        }])
        .select(`
          *,
          student:student_id(id, first_name, last_name, belt_color),
          exercise:exercise_id(id, name, measurement_type),
          recorder:recorded_by(id, first_name, last_name, role),
          verifier:verified_by(id, first_name, last_name, role)
        `)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to record fitness measurement' };
    }
  },

  // Verify a fitness record
  async verifyFitnessRecord(recordId: string, verifiedBy: string): Promise<ApiResponse<FitnessRecord>> {
    try {
      const { data, error } = await supabase
        .from('fitness_records')
        .update({ verified_by: verifiedBy })
        .eq('id', recordId)
        .select(`
          *,
          student:student_id(id, first_name, last_name, belt_color),
          exercise:exercise_id(id, name, measurement_type),
          recorder:recorded_by(id, first_name, last_name, role),
          verifier:verified_by(id, first_name, last_name, role)
        `)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to verify fitness record' };
    }
  },

  // Get student fitness progress for an exercise
  async getStudentFitnessProgress(studentId: string, exerciseId: string): Promise<ApiResponse<{
    records: FitnessRecord[];
    bestRecord: FitnessRecord | null;
    latestRecord: FitnessRecord | null;
    improvementPercentage: number;
  }>> {
    try {
      const { data: records, error } = await supabase
        .from('fitness_records')
        .select(`
          *,
          student:student_id(id, first_name, last_name, belt_color),
          exercise:exercise_id(id, name, measurement_type),
          recorder:recorded_by(id, first_name, last_name, role),
          verifier:verified_by(id, first_name, last_name, role)
        `)
        .eq('student_id', studentId)
        .eq('exercise_id', exerciseId)
        .order('recorded_at', { ascending: true });

      if (error) {
        return { data: null, error: error.message };
      }

      if (!records || records.length === 0) {
        return {
          data: {
            records: [],
            bestRecord: null,
            latestRecord: null,
            improvementPercentage: 0
          },
          error: null
        };
      }

      // Find best record (depends on measurement type)
      const exercise = records[0].exercise;
      let bestRecord: FitnessRecord;
      
      if (exercise?.measurement_type === 'time') {
        // For time, lower is better
        bestRecord = records.reduce((best, current) => 
          current.value < best.value ? current : best
        );
      } else {
        // For count, weight, distance, higher is better
        bestRecord = records.reduce((best, current) => 
          current.value > best.value ? current : best
        );
      }

      const latestRecord = records[records.length - 1];
      const firstRecord = records[0];

      // Calculate improvement percentage
      let improvementPercentage = 0;
      if (records.length > 1) {
        if (exercise?.measurement_type === 'time') {
          // For time, improvement is reduction in time
          improvementPercentage = ((firstRecord.value - latestRecord.value) / firstRecord.value) * 100;
        } else {
          // For other types, improvement is increase in value
          improvementPercentage = ((latestRecord.value - firstRecord.value) / firstRecord.value) * 100;
        }
      }

      return {
        data: {
          records,
          bestRecord,
          latestRecord,
          improvementPercentage
        },
        error: null
      };
    } catch (error) {
      return { data: null, error: 'Failed to get fitness progress' };
    }
  },

  // Get fitness summary for a student
  async getStudentFitnessSummary(studentId: string): Promise<ApiResponse<Array<{
    exercise: Exercise;
    recordCount: number;
    bestValue: number;
    latestValue: number;
    lastRecorded: string;
  }>>> {
    try {
      const { data: records, error } = await supabase
        .from('fitness_records')
        .select(`
          *,
          exercise:exercise_id(*)
        `)
        .eq('student_id', studentId)
        .order('recorded_at', { ascending: false });

      if (error) {
        return { data: null, error: error.message };
      }

      // Group records by exercise
      const exerciseMap = new Map<string, FitnessRecord[]>();
      records?.forEach(record => {
        const exerciseId = record.exercise_id;
        if (!exerciseMap.has(exerciseId)) {
          exerciseMap.set(exerciseId, []);
        }
        exerciseMap.get(exerciseId)!.push(record);
      });

      const summary = Array.from(exerciseMap.entries()).map(([exerciseId, exerciseRecords]) => {
        const exercise = exerciseRecords[0].exercise!;
        const sortedRecords = exerciseRecords.sort((a, b) => 
          new Date(a.recorded_at!).getTime() - new Date(b.recorded_at!).getTime()
        );

        let bestValue: number;
        if (exercise.measurement_type === 'time') {
          bestValue = Math.min(...exerciseRecords.map(r => r.value));
        } else {
          bestValue = Math.max(...exerciseRecords.map(r => r.value));
        }

        const latestRecord = sortedRecords[sortedRecords.length - 1];

        return {
          exercise,
          recordCount: exerciseRecords.length,
          bestValue,
          latestValue: latestRecord.value,
          lastRecorded: latestRecord.recorded_at!
        };
      });

      return { data: summary, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to get fitness summary' };
    }
  }
};
