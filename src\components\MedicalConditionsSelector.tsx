import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, Plus, X, AlertTriangle } from 'lucide-react';
import { supabase } from '../lib/supabase';

interface MedicalCondition {
  id: string;
  name: string;
  description?: string;
  severity_level?: string;
  requires_attention: boolean;
}

interface MedicalConditionCategory {
  id: string;
  name: string;
  description?: string;
  conditions: MedicalCondition[];
}

interface SelectedCondition {
  condition_id: string;
  condition_name: string;
  notes?: string;
  severity?: string;
  medication?: string;
}

interface MedicalConditionsSelectorProps {
  selectedConditions: SelectedCondition[];
  onChange: (conditions: SelectedCondition[]) => void;
}

export const MedicalConditionsSelector: React.FC<MedicalConditionsSelectorProps> = ({
  selectedConditions,
  onChange
}) => {
  const [categories, setCategories] = useState<MedicalConditionCategory[]>([]);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);
  const [customCondition, setCustomCondition] = useState('');
  const [showCustomForm, setShowCustomForm] = useState(false);

  useEffect(() => {
    loadMedicalConditions();
  }, []);

  const loadMedicalConditions = async () => {
    try {
      // Load categories
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('medical_condition_categories')
        .select('*')
        .order('display_order');

      if (categoriesError) throw categoriesError;

      // Load conditions
      const { data: conditionsData, error: conditionsError } = await supabase
        .from('medical_conditions')
        .select('*')
        .order('display_order');

      if (conditionsError) throw conditionsError;

      // Group conditions by category
      const categoriesWithConditions = categoriesData.map(category => ({
        ...category,
        conditions: conditionsData.filter(condition => condition.category_id === category.id)
      }));

      setCategories(categoriesWithConditions);
    } catch (error) {
      console.error('Failed to load medical conditions:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleCategory = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  const isConditionSelected = (conditionId: string) => {
    return selectedConditions.some(sc => sc.condition_id === conditionId);
  };

  const toggleCondition = (condition: MedicalCondition) => {
    const isSelected = isConditionSelected(condition.id);
    
    if (isSelected) {
      // Remove condition
      onChange(selectedConditions.filter(sc => sc.condition_id !== condition.id));
    } else {
      // Add condition
      const newCondition: SelectedCondition = {
        condition_id: condition.id,
        condition_name: condition.name,
        notes: '',
        severity: condition.severity_level || 'mild',
        medication: ''
      };
      onChange([...selectedConditions, newCondition]);
    }
  };

  const updateConditionDetails = (conditionId: string, field: keyof SelectedCondition, value: string) => {
    onChange(selectedConditions.map(sc => 
      sc.condition_id === conditionId 
        ? { ...sc, [field]: value }
        : sc
    ));
  };

  const addCustomCondition = () => {
    if (!customCondition.trim()) return;
    
    const newCondition: SelectedCondition = {
      condition_id: `custom_${Date.now()}`,
      condition_name: customCondition.trim(),
      notes: '',
      severity: 'mild',
      medication: ''
    };
    
    onChange([...selectedConditions, newCondition]);
    setCustomCondition('');
    setShowCustomForm(false);
  };

  const removeCondition = (conditionId: string) => {
    onChange(selectedConditions.filter(sc => sc.condition_id !== conditionId));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Selected Conditions */}
      {selectedConditions.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-700">Selected Medical Conditions</h4>
          {selectedConditions.map((selected) => (
            <div key={selected.condition_id} className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-gray-900">{selected.condition_name}</span>
                  {selected.condition_name.toLowerCase().includes('severe') && (
                    <AlertTriangle className="h-4 w-4 text-red-500" />
                  )}
                </div>
                <button
                  onClick={() => removeCondition(selected.condition_id)}
                  className="text-gray-400 hover:text-red-500"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Severity
                  </label>
                  <select
                    value={selected.severity || 'mild'}
                    onChange={(e) => updateConditionDetails(selected.condition_id, 'severity', e.target.value)}
                    className="block w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option value="mild">Mild</option>
                    <option value="moderate">Moderate</option>
                    <option value="severe">Severe</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Medication
                  </label>
                  <input
                    type="text"
                    value={selected.medication || ''}
                    onChange={(e) => updateConditionDetails(selected.condition_id, 'medication', e.target.value)}
                    placeholder="e.g., Inhaler, EpiPen"
                    className="block w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
                
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Notes
                  </label>
                  <input
                    type="text"
                    value={selected.notes || ''}
                    onChange={(e) => updateConditionDetails(selected.condition_id, 'notes', e.target.value)}
                    placeholder="Additional details"
                    className="block w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Medical Conditions Browser */}
      <div>
        <h4 className="text-sm font-medium text-gray-700 mb-3">Browse Medical Conditions</h4>
        <div className="border border-gray-200 rounded-lg max-h-64 overflow-y-auto">
          {categories.map((category) => (
            <div key={category.id} className="border-b border-gray-200 last:border-b-0">
              <button
                onClick={() => toggleCategory(category.id)}
                className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50"
              >
                <span className="font-medium text-gray-900">{category.name}</span>
                {expandedCategories.has(category.id) ? (
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-gray-500" />
                )}
              </button>
              
              {expandedCategories.has(category.id) && (
                <div className="px-4 pb-3">
                  {category.conditions.map((condition) => (
                    <div key={condition.id} className="flex items-center space-x-3 py-2">
                      <input
                        type="checkbox"
                        checked={isConditionSelected(condition.id)}
                        onChange={() => toggleCondition(condition)}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      />
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-900">{condition.name}</span>
                          {condition.requires_attention && (
                            <AlertTriangle className="h-3 w-3 text-orange-500" />
                          )}
                        </div>
                        {condition.description && (
                          <p className="text-xs text-gray-500 mt-1">{condition.description}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Custom Condition */}
      <div>
        {!showCustomForm ? (
          <button
            onClick={() => setShowCustomForm(true)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Custom Condition
          </button>
        ) : (
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={customCondition}
              onChange={(e) => setCustomCondition(e.target.value)}
              placeholder="Enter custom medical condition"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              onKeyPress={(e) => e.key === 'Enter' && addCustomCondition()}
            />
            <button
              onClick={addCustomCondition}
              disabled={!customCondition.trim()}
              className="px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              Add
            </button>
            <button
              onClick={() => {
                setShowCustomForm(false);
                setCustomCondition('');
              }}
              className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Cancel
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
