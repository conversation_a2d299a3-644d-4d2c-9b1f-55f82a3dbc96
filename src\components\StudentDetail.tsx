import React, { useState, useEffect } from 'react';
import { 
  User, 
  Calendar, 
  Phone, 
  Shield, 
  Heart, 
  Award, 
  Activity,
  Edit,
  X,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import { attendanceService, gradingService, fitnessService } from '../services';
import type { Student, Attendance, Grading, FitnessRecord } from '../types';
import { useAuth } from '../contexts/AuthContext';

interface StudentDetailProps {
  student: Student;
  onEdit?: () => void;
  onClose?: () => void;
}

export const StudentDetail: React.FC<StudentDetailProps> = ({
  student,
  onEdit,
  onClose
}) => {
  const { state } = useAuth();
  const { profile } = state;
  const [activeTab, setActiveTab] = useState<'overview' | 'attendance' | 'grading' | 'fitness'>('overview');
  const [attendance, setAttendance] = useState<Attendance[]>([]);
  const [gradings, setGradings] = useState<Grading[]>([]);
  const [fitnessRecords, setFitnessRecords] = useState<FitnessRecord[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadStudentData();
  }, [student.id, activeTab]);

  const loadStudentData = async () => {
    setLoading(true);
    
    try {
      if (activeTab === 'attendance') {
        const result = await attendanceService.getStudentAttendance(student.id);
        if (!result.error) {
          setAttendance(result.data);
        }
      } else if (activeTab === 'grading') {
        const result = await gradingService.getStudentGradings(student.id);
        if (!result.error) {
          setGradings(result.data);
        }
      } else if (activeTab === 'fitness') {
        const result = await fitnessService.getFitnessRecords(student.id);
        if (!result.error) {
          setFitnessRecords(result.data);
        }
      }
    } catch (error) {
      console.error('Failed to load student data:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return age - 1;
    }
    return age;
  };

  const getBeltColorBadge = (beltColor: string) => {
    const colorMap: Record<string, string> = {
      white: 'bg-gray-100 text-gray-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      orange: 'bg-orange-100 text-orange-800',
      green: 'bg-green-100 text-green-800',
      blue: 'bg-blue-100 text-blue-800',
      purple: 'bg-purple-100 text-purple-800',
      brown: 'bg-amber-100 text-amber-800',
      black: 'bg-gray-800 text-white'
    };

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${colorMap[beltColor] || 'bg-gray-100 text-gray-800'}`}>
        {beltColor.charAt(0).toUpperCase() + beltColor.slice(1)} Belt
      </span>
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'attended_paid':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'attended_unpaid':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'absent':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: User },
    { id: 'attendance', label: 'Attendance', icon: Calendar },
    { id: 'grading', label: 'Grading', icon: Award },
    { id: 'fitness', label: 'Fitness', icon: Activity }
  ];

  return (
    <div className="max-w-6xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 bg-indigo-600 text-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="h-12 w-12 rounded-full bg-indigo-500 flex items-center justify-center">
              <span className="text-lg font-medium">
                {student.first_name[0]}{student.last_name[0]}
              </span>
            </div>
            <div>
              <h1 className="text-2xl font-bold">
                {student.first_name} {student.last_name}
              </h1>
              <div className="flex items-center space-x-4 mt-1">
                {getBeltColorBadge(student.belt_color)}
                <span className="text-indigo-200">
                  Age: {calculateAge(student.date_of_birth)}
                </span>
                {!student.active && (
                  <span className="bg-red-500 text-white px-2 py-1 rounded text-sm">
                    Inactive
                  </span>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {onEdit && (profile?.role === 'instructor' || profile?.role === 'admin') && (
              <button
                onClick={onEdit}
                className="bg-indigo-500 hover:bg-indigo-400 text-white px-4 py-2 rounded-md flex items-center space-x-2"
              >
                <Edit className="h-4 w-4" />
                <span>Edit</span>
              </button>
            )}
            {onClose && (
              <button
                onClick={onClose}
                className="text-indigo-200 hover:text-white"
              >
                <X className="h-6 w-6" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Personal Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                  <User className="h-5 w-5 mr-2 text-indigo-600" />
                  Personal Information
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">Date of Birth:</span>
                    <span className="text-sm font-medium">
                      {new Date(student.date_of_birth).toLocaleDateString()}
                    </span>
                  </div>
                  
                  {student.phone && (
                    <div className="flex items-center space-x-3">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">Phone:</span>
                      <span className="text-sm font-medium">{student.phone}</span>
                    </div>
                  )}
                  
                  {student.guardian && (
                    <div className="flex items-center space-x-3">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">Guardian:</span>
                      <span className="text-sm font-medium">
                        {student.guardian.first_name} {student.guardian.last_name}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                  <Shield className="h-5 w-5 mr-2 text-indigo-600" />
                  Martial Arts Information
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Award className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">Current Belt:</span>
                    {getBeltColorBadge(student.belt_color)}
                  </div>
                  
                  {student.last_graded && (
                    <div className="flex items-center space-x-3">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">Last Graded:</span>
                      <span className="text-sm font-medium">
                        {new Date(student.last_graded).toLocaleDateString()}
                      </span>
                    </div>
                  )}
                  
                  {student.insurance_expiry && (
                    <div className="flex items-center space-x-3">
                      <Shield className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">Insurance Expiry:</span>
                      <span className="text-sm font-medium">
                        {new Date(student.insurance_expiry).toLocaleDateString()}
                      </span>
                    </div>
                  )}
                  
                  {student.licence_number && (
                    <div className="flex items-center space-x-3">
                      <Shield className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">Licence Number:</span>
                      <span className="text-sm font-medium">{student.licence_number}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Consent Information */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <Heart className="h-5 w-5 mr-2 text-indigo-600" />
                Consent & Medical
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  {student.consent_social_media ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                  <span className="text-sm">Social Media Consent</span>
                </div>
                
                <div className="flex items-center space-x-3">
                  {student.consent_assumption_risk ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                  <span className="text-sm">Assumption of Risk Consent</span>
                </div>
              </div>
              
              {student.medical_conditions && student.medical_conditions.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Medical Conditions:</h4>
                  <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                    <ul className="text-sm text-yellow-800 space-y-1">
                      {student.medical_conditions.map((condition, index) => {
                        // Handle different possible formats of medical conditions
                        let conditionText = '';
                        
                        if (typeof condition === 'string') {
                          conditionText = condition;
                        } else if (condition && typeof condition === 'object') {
                          // Handle object format with condition_name property
                          conditionText = condition.condition_name || condition.name || 'Unknown Condition';
                          
                          // Ensure we're getting a string value
                          if (typeof conditionText !== 'string') {
                            conditionText = 'Unknown Condition';
                          }
                        } else {
                          conditionText = 'Unknown Condition';
                        }
                        
                        return (
                          <li key={index}>
                            • {conditionText}
                            {condition && typeof condition === 'object' && condition.notes && (
                              <span className="text-xs block ml-4 text-yellow-700">
                                Note: {condition.notes}
                              </span>
                            )}
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Other tabs content would continue here but truncated for length */}
        {activeTab !== 'overview' && (
          <div className="text-center py-8 text-gray-500">
            {loading ? (
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
            ) : (
              `${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} content will be displayed here`
            )}
          </div>
        )}
      </div>
    </div>
  );
};
