-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Profiles table (extends auth.users)
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  date_of_birth DATE,
  phone TEXT,
  address JSONB,
  avatar_url TEXT,
  role TEXT CHECK (role IN ('admin', 'instructor', 'student', 'guardian')) NOT NULL DEFAULT 'student',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Students table (can exist without user accounts)
CREATE TABLE students (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  date_of_birth DATE NOT NULL,
  address J<PERSON>N<PERSON>,
  phone TEXT,
  emergency_contact JSON<PERSON>,
  belt_color TEXT NOT NULL DEFAULT 'white',
  last_graded DATE,
  insurance_expiry DATE,
  licence_number TEXT,
  guardian_id UUID REFERENCES profiles(id),
  medical_conditions JSONB DEFAULT '[]',
  consent_social_media BOOLEAN DEFAULT FALSE,
  consent_assumption_risk BOOLEAN DEFAULT FALSE,
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Account link requests
CREATE TABLE account_link_requests (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  student_id UUID REFERENCES students(id) NOT NULL,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  relationship_type TEXT CHECK (relationship_type IN ('self', 'guardian')) NOT NULL,
  status TEXT CHECK (status IN ('pending', 'approved', 'rejected')) DEFAULT 'pending',
  requested_by UUID REFERENCES profiles(id) NOT NULL,
  reviewed_by UUID REFERENCES profiles(id),
  request_message TEXT,
  review_notes TEXT,
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  reviewed_at TIMESTAMP WITH TIME ZONE
);

-- Sessions table
CREATE TABLE sessions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  instructor_id UUID REFERENCES profiles(id) NOT NULL,
  max_capacity INTEGER DEFAULT 20,
  session_type TEXT DEFAULT 'training',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Attendance table
CREATE TABLE attendance (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  session_id UUID REFERENCES sessions(id) NOT NULL,
  student_id UUID REFERENCES students(id) NOT NULL,
  status TEXT CHECK (status IN ('attended_paid', 'attended_unpaid', 'absent')) NOT NULL,
  recorded_by UUID REFERENCES profiles(id) NOT NULL,
  recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(session_id, student_id)
);

-- Syllabus table
CREATE TABLE syllabus (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  belt_color TEXT NOT NULL,
  category TEXT NOT NULL,
  skill_name TEXT NOT NULL,
  description TEXT,
  order_index INTEGER NOT NULL DEFAULT 0
);

-- Gradings table
CREATE TABLE gradings (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  student_id UUID REFERENCES students(id) NOT NULL,
  syllabus_id UUID REFERENCES syllabus(id) NOT NULL,
  result TEXT CHECK (result IN ('pass', 'fail', 'not_assessed')) DEFAULT 'not_assessed',
  graded_by UUID REFERENCES profiles(id) NOT NULL,
  graded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notes TEXT,
  UNIQUE(student_id, syllabus_id)
);

-- Exercises table
CREATE TABLE exercises (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  measurement_type TEXT CHECK (measurement_type IN ('count', 'time', 'weight', 'distance')) NOT NULL,
  created_by UUID REFERENCES profiles(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Fitness records table
CREATE TABLE fitness_records (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  student_id UUID REFERENCES students(id) NOT NULL,
  exercise_id UUID REFERENCES exercises(id) NOT NULL,
  value NUMERIC NOT NULL,
  recorded_by UUID REFERENCES profiles(id) NOT NULL,
  verified_by UUID REFERENCES profiles(id),
  recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notes TEXT
);

-- RLS Policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE account_link_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE syllabus ENABLE ROW LEVEL SECURITY;
ALTER TABLE gradings ENABLE ROW LEVEL SECURITY;
ALTER TABLE exercises ENABLE ROW LEVEL SECURITY;
ALTER TABLE fitness_records ENABLE ROW LEVEL SECURITY;

-- Profile policies
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);

-- Students policies
CREATE POLICY "Instructors can view all students" ON students FOR SELECT 
USING (EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('instructor', 'admin')));

CREATE POLICY "Users can view linked students" ON students FOR SELECT 
USING (user_id = auth.uid() OR guardian_id = auth.uid());

-- Link request policies
CREATE POLICY "Users can view own requests" ON account_link_requests FOR SELECT 
USING (requested_by = auth.uid());

CREATE POLICY "Instructors can view all requests" ON account_link_requests FOR SELECT 
USING (EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role IN ('instructor', 'admin')));

-- Functions for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_students_updated_at BEFORE UPDATE ON students FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();


