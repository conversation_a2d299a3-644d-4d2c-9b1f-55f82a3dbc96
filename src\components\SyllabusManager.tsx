import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, BookOpen, Target, Award, Save, X } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { supabase } from '../services/supabaseClient';
import { useAuth } from '../contexts/AuthContext';

interface SyllabusItem {
  id: string;
  belt_level: string;
  category: string;
  skill_name: string;
  description?: string;
  requirements?: string;
  difficulty_level: number;
  is_required: boolean;
  display_order: number;
  created_at: string;
}

interface SyllabusForm {
  belt_level: string;
  category: string;
  skill_name: string;
  description: string;
  requirements: string;
  difficulty_level: number;
  is_required: boolean;
  display_order: number;
}

const BELT_LEVELS = [
  'white', 'yellow', 'orange', 'green', 'blue', 'purple', 'brown', 'black'
];

const CATEGORIES = [
  'Techniques', 'Forms/Kata', 'Sparring', 'Self-Defense', 'Weapons', 'Theory', 'Physical Fitness'
];

export const SyllabusManager: React.FC = () => {
  const { state } = useAuth();
  const { profile } = state;
  const [syllabusItems, setSyllabusItems] = useState<SyllabusItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState<SyllabusItem | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [filterBelt, setFilterBelt] = useState<string>('all');
  const [filterCategory, setFilterCategory] = useState<string>('all');

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<SyllabusForm>();

  useEffect(() => {
    loadSyllabusItems();
  }, []);

  const loadSyllabusItems = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('syllabus')
        .select('*')
        .order('belt_level')
        .order('display_order');

      if (error) {
        setError(error.message);
      } else {
        setSyllabusItems(data || []);
      }
    } catch (err) {
      setError('Failed to load syllabus items');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: SyllabusForm) => {
    if (!profile) return;

    setError(null);

    try {
      if (editingItem) {
        // Update existing item
        const { error } = await supabase
          .from('syllabus')
          .update(data)
          .eq('id', editingItem.id);

        if (error) {
          setError(error.message);
          return;
        }
      } else {
        // Create new item
        const { error } = await supabase
          .from('syllabus')
          .insert([data]);

        if (error) {
          setError(error.message);
          return;
        }
      }

      // Reload data and close form
      await loadSyllabusItems();
      setShowForm(false);
      setEditingItem(null);
      reset();
    } catch (err) {
      setError('Failed to save syllabus item');
    }
  };

  const handleEdit = (item: SyllabusItem) => {
    setEditingItem(item);
    reset({
      belt_level: item.belt_level,
      category: item.category,
      skill_name: item.skill_name,
      description: item.description || '',
      requirements: item.requirements || '',
      difficulty_level: item.difficulty_level,
      is_required: item.is_required,
      display_order: item.display_order
    });
    setShowForm(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this syllabus item?')) return;

    try {
      const { error } = await supabase
        .from('syllabus')
        .delete()
        .eq('id', id);

      if (error) {
        setError(error.message);
      } else {
        await loadSyllabusItems();
      }
    } catch (err) {
      setError('Failed to delete syllabus item');
    }
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingItem(null);
    reset();
  };

  const filteredItems = syllabusItems.filter(item => {
    const beltMatch = filterBelt === 'all' || item.belt_level === filterBelt;
    const categoryMatch = filterCategory === 'all' || item.category === filterCategory;
    return beltMatch && categoryMatch;
  });

  const groupedItems = filteredItems.reduce((acc, item) => {
    const key = `${item.belt_level}-${item.category}`;
    if (!acc[key]) {
      acc[key] = {
        belt_level: item.belt_level,
        category: item.category,
        items: []
      };
    }
    acc[key].items.push(item);
    return acc;
  }, {} as Record<string, { belt_level: string; category: string; items: SyllabusItem[] }>);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <BookOpen className="h-6 w-6 text-indigo-600 mr-3" />
              Syllabus Management
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Manage curriculum requirements for each belt level
            </p>
          </div>
          
          <button
            onClick={() => setShowForm(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Syllabus Item
          </button>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Filter by Belt Level
            </label>
            <select
              value={filterBelt}
              onChange={(e) => setFilterBelt(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="all">All Belt Levels</option>
              {BELT_LEVELS.map(belt => (
                <option key={belt} value={belt}>
                  {belt.charAt(0).toUpperCase() + belt.slice(1)} Belt
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Filter by Category
            </label>
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="all">All Categories</option>
              {CATEGORIES.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-600">{error}</div>
        </div>
      )}

      {/* Form */}
      {showForm && (
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {editingItem ? 'Edit Syllabus Item' : 'Add New Syllabus Item'}
          </h3>
          
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Belt Level *
                </label>
                <select
                  {...register('belt_level', { required: 'Belt level is required' })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">Select Belt Level</option>
                  {BELT_LEVELS.map(belt => (
                    <option key={belt} value={belt}>
                      {belt.charAt(0).toUpperCase() + belt.slice(1)} Belt
                    </option>
                  ))}
                </select>
                {errors.belt_level && (
                  <p className="mt-1 text-sm text-red-600">{errors.belt_level.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category *
                </label>
                <select
                  {...register('category', { required: 'Category is required' })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">Select Category</option>
                  {CATEGORIES.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
                {errors.category && (
                  <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Skill Name *
              </label>
              <input
                type="text"
                {...register('skill_name', { required: 'Skill name is required' })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="e.g., Front Kick, Basic Kata 1"
              />
              {errors.skill_name && (
                <p className="mt-1 text-sm text-red-600">{errors.skill_name.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                {...register('description')}
                rows={3}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Detailed description of the skill or technique"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Requirements
              </label>
              <textarea
                {...register('requirements')}
                rows={2}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Specific requirements or criteria for mastery"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Difficulty Level (1-5)
                </label>
                <input
                  type="number"
                  min="1"
                  max="5"
                  {...register('difficulty_level', { 
                    required: 'Difficulty level is required',
                    min: { value: 1, message: 'Minimum difficulty is 1' },
                    max: { value: 5, message: 'Maximum difficulty is 5' }
                  })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                />
                {errors.difficulty_level && (
                  <p className="mt-1 text-sm text-red-600">{errors.difficulty_level.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Display Order
                </label>
                <input
                  type="number"
                  min="0"
                  {...register('display_order', { required: 'Display order is required' })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                />
                {errors.display_order && (
                  <p className="mt-1 text-sm text-red-600">{errors.display_order.message}</p>
                )}
              </div>

              <div className="flex items-center pt-6">
                <input
                  type="checkbox"
                  {...register('is_required')}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-900">
                  Required for belt promotion
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={handleCancel}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <Save className="h-4 w-4 mr-2" />
                {editingItem ? 'Update' : 'Create'} Item
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Syllabus Items */}
      <div className="space-y-6">
        {Object.values(groupedItems).length === 0 ? (
          <div className="bg-white shadow rounded-lg p-12 text-center">
            <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No syllabus items</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by creating your first syllabus item.
            </p>
          </div>
        ) : (
          Object.values(groupedItems).map((group) => (
            <div key={`${group.belt_level}-${group.category}`} className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  {group.belt_level.charAt(0).toUpperCase() + group.belt_level.slice(1)} Belt - {group.category}
                </h3>
              </div>
              <div className="divide-y divide-gray-200">
                {group.items.map((item) => (
                  <div key={item.id} className="px-6 py-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h4 className="text-sm font-medium text-gray-900">{item.skill_name}</h4>
                          {item.is_required && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              Required
                            </span>
                          )}
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Level {item.difficulty_level}
                          </span>
                        </div>
                        {item.description && (
                          <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                        )}
                        {item.requirements && (
                          <p className="text-sm text-gray-500 mt-1">
                            <strong>Requirements:</strong> {item.requirements}
                          </p>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => handleEdit(item)}
                          className="text-indigo-600 hover:text-indigo-900 p-1"
                          title="Edit"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(item.id)}
                          className="text-red-600 hover:text-red-900 p-1"
                          title="Delete"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};
