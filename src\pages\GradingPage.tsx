import React, { useState } from 'react';
import { Award, Users, BookOpen, TrendingUp } from 'lucide-react';
import { SyllabusManager } from '../components/SyllabusManager';
import { StudentGradingInterface } from '../components/StudentGradingInterface';

export const GradingPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'syllabus' | 'gradings'>('overview');

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 flex items-center">
          <Award className="h-8 w-8 mr-3 text-indigo-600" />
          Grading System
        </h1>
        <p className="mt-2 text-gray-600">Track student progress and manage belt gradings</p>

        {/* Tab Navigation */}
        <div className="mt-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('overview')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'overview'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Overview
              </button>
              <button
                onClick={() => setActiveTab('syllabus')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'syllabus'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Syllabus Management
              </button>
              <button
                onClick={() => setActiveTab('gradings')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'gradings'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Student Gradings
              </button>
            </nav>
          </div>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <>
          {/* Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Students Ready for Grading</p>
              <p className="text-2xl font-bold text-gray-900">--</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <BookOpen className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Syllabus Items</p>
              <p className="text-2xl font-bold text-gray-900">--</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Award className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Recent Gradings</p>
              <p className="text-2xl font-bold text-gray-900">--</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <TrendingUp className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pass Rate</p>
              <p className="text-2xl font-bold text-gray-900">--%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Coming Soon Content */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 text-center">
          <Award className="mx-auto h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Grading System Coming Soon</h3>
          <p className="text-gray-500 mb-6">
            This feature will include:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left max-w-2xl mx-auto">
            <div className="space-y-2">
              <div className="flex items-center text-sm text-gray-600">
                <div className="w-2 h-2 bg-indigo-600 rounded-full mr-3"></div>
                Student progress tracking by belt level
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <div className="w-2 h-2 bg-indigo-600 rounded-full mr-3"></div>
                Syllabus management and skill assessment
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <div className="w-2 h-2 bg-indigo-600 rounded-full mr-3"></div>
                Digital grading sheets and records
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center text-sm text-gray-600">
                <div className="w-2 h-2 bg-indigo-600 rounded-full mr-3"></div>
                Belt promotion recommendations
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <div className="w-2 h-2 bg-indigo-600 rounded-full mr-3"></div>
                Progress reports for students and guardians
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <div className="w-2 h-2 bg-indigo-600 rounded-full mr-3"></div>
                Grading history and analytics
              </div>
            </div>
          </div>
        </div>
      </div>
        </>
      )}

      {activeTab === 'syllabus' && (
        <SyllabusManager />
      )}

      {activeTab === 'gradings' && (
        <StudentGradingInterface />
      )}
    </div>
  );
};
