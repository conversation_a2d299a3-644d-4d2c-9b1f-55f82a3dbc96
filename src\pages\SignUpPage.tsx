import React, { useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { signUp, getErrorMessage, createInitialProfile } from '../services/authService';
import { useAuth } from '../contexts/AuthContext';
import { UserPlus, GraduationCap, ShieldCheck, Key, Calendar, Users, ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { PageCenter } from '../components/PageLayout';
import { ChildForm, type ChildData } from '../components/ChildForm';
import { studentService } from '../services';

const INSTRUCTOR_CODE = 'BMAF';

const getRoles = (showInstructor: boolean) => {
  if (showInstructor) {
    return [{ value: 'instructor', label: 'Instructor', icon: <ShieldCheck className="w-5 h-5 mr-2" /> }];
  }
  return [
    { value: 'student', label: 'Student', icon: <GraduationCap className="w-5 h-5 mr-2" /> },
    { value: 'guardian', label: 'Guardian', icon: <UserPlus className="w-5 h-5 mr-2" /> },
  ];
};

const createSchema = (showInstructor: boolean) => z.object({
  email: z.string().email('Invalid email'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string(),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  role: z.enum(showInstructor ? ['instructor', 'student', 'guardian'] : ['student', 'guardian'], { required_error: 'Role is required' }),
  instructorCode: z.string().optional(),
  dateOfBirth: z.string().optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
}).refine((data) => {
  if (data.role === 'instructor' && data.instructorCode !== INSTRUCTOR_CODE) {
    return false;
  }
  return true;
}, {
  message: "Invalid instructor code",
  path: ["instructorCode"],
}).refine((data) => {
  if (data.role === 'student' && data.dateOfBirth) {
    const today = new Date();
    const birthDate = new Date(data.dateOfBirth);
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    const calculatedAge = monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate()) ? age - 1 : age;
    return calculatedAge >= 16;
  }
  return true;
}, {
  message: "Students under 16 must be registered by a guardian",
  path: ["dateOfBirth"],
});

// Create a base type that includes all possible fields
type SignUpFormData = {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  role: 'instructor' | 'student' | 'guardian';
  instructorCode?: string;
  dateOfBirth?: string;
  phone?: string;
  address?: string;
};

export const SignUpPage: React.FC = () => {
  const { dispatch } = useAuth();
  const [success, setSuccess] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const navigate = useNavigate();
  const [step, setStep] = useState<'form' | 'children'>('form');
  const [children, setChildren] = useState<ChildData[]>([]);
  const [guardianData, setGuardianData] = useState<any>(null);
  
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    setError,
    reset,
    watch,
    setValue,
  } = useForm<SignUpFormData>({ 
    defaultValues: { role: 'student', instructorCode: '' } 
  });
    const password = watch('password');
  const selectedRole = watch('role');
  const instructorCodeValue = watch('instructorCode') || '';
  const [passwordStrength, setPasswordStrength] = useState(0);
    // Determine if instructor option should be shown
  const showInstructor = instructorCodeValue === INSTRUCTOR_CODE;
  const roles = getRoles(showInstructor);
  
  // Immediately update role when code changes
  React.useEffect(() => {
    if (instructorCodeValue === INSTRUCTOR_CODE) {
      setValue('role', 'instructor');
      console.log('Instructor code valid - setting role to instructor');
    }
  }, [instructorCodeValue, setValue]);
    // Update role when instructor code status changes
  React.useEffect(() => {
    if (showInstructor) {
      // When valid code is entered, set role to instructor
      setValue('role', 'instructor');
      console.log('Setting role to instructor');
    } else if (selectedRole === 'instructor') {
      // If instructor code is removed and user was instructor
      setValue('role', 'student');
      console.log('Setting role back to student');
    }
  }, [showInstructor, selectedRole, setValue]);
  // Password strength calculation
  React.useEffect(() => {
    if (password) {
      let strength = 0;
      // Length check
      if (password.length >= 6) strength += 20;
      if (password.length >= 10) strength += 10;
      // Character variety check
      if (/[A-Z]/.test(password)) strength += 20; // Uppercase
      if (/[a-z]/.test(password)) strength += 10; // Lowercase
      if (/[0-9]/.test(password)) strength += 20; // Numbers
      if (/[^A-Za-z0-9]/.test(password)) strength += 20; // Special characters
      setPasswordStrength(strength);
    } else {
      setPasswordStrength(0);
    }
  }, [password]);  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return age - 1;
    }
    return age;
  };

  const onSubmit = async (data: SignUpFormData) => {
    setErrorMsg('');
    setSuccess(false);

    // Force correct role based on instructor code
    if (instructorCodeValue === INSTRUCTOR_CODE) {
      data.role = 'instructor';
      console.log('Form submission - set role to instructor');
    } else if (data.role === 'instructor') {
      setError('instructorCode', { message: 'Valid instructor code is required' });
      setErrorMsg('Valid instructor code is required to sign up as instructor');
      return;
    }

    // Age check for students
    if (data.role === 'student' && data.dateOfBirth) {
      const age = calculateAge(data.dateOfBirth);
      if (age < 16) {
        setErrorMsg('Students under 16 must be registered by a guardian. Please have your guardian sign up and add you as their child.');
        return;
      }
    }

    // Guardian flow - proceed to children step
    if (data.role === 'guardian') {
      setGuardianData(data);
      setStep('children');
      return;
    }

    // Log what's being submitted
    console.log('Submitting form with role:', data.role);

    // Validate password confirmation
    if (data.password !== data.confirmPassword) {
      setError('confirmPassword', { message: "Passwords don't match" });
      setErrorMsg("Passwords don't match");
      return;
    }

    await completeSignUp(data);
  };

  const completeSignUp = async (data: SignUpFormData, childrenData?: ChildData[]) => {
    const { error, data: result } = await signUp(data.email, data.password);
    if (error) {
      setError('email', { message: getErrorMessage(error) });
      setErrorMsg(getErrorMessage(error));
      return;
    }

    if (result?.user) {
      console.log(`Creating profile with role: ${data.role}`);
      const roleToUse = instructorCodeValue === INSTRUCTOR_CODE ? 'instructor' : data.role;
      console.log(`Final role being used for profile creation: ${roleToUse}`);

      // Use our robust profile creation function with retry mechanism
      const { error: profileError } = await createInitialProfile(
        result.user.id,
        data.firstName,
        data.lastName,
        roleToUse
      );

      if (profileError) {
        console.error('Profile creation failed completely:', profileError);
        setErrorMsg('Sign up succeeded, but failed to create profile. Please contact support or try signing in.');
        return;
      }

      // If guardian, create children records
      if (data.role === 'guardian' && childrenData && childrenData.length > 0) {
        try {
          for (const child of childrenData) {
            const studentData = {
              first_name: child.firstName,
              last_name: child.lastName,
              date_of_birth: child.dateOfBirth,
              guardian_id: result.user.id,
              medical_conditions: child.medicalConditions.map(mc => mc.condition_name),
              consent_social_media: child.consentSocialMedia,
              consent_assumption_risk: child.consentAssumptionRisk,
              belt_color: 'white',
              active: true
            };

            const childResult = await studentService.createStudent(studentData);
            if (childResult.error) {
              console.error('Failed to create child record:', childResult.error);
              setErrorMsg(`Failed to create record for ${child.firstName}. Please contact support.`);
              return;
            }
          }
        } catch (error) {
          console.error('Error creating children records:', error);
          setErrorMsg('Failed to create children records. Please contact support.');
          return;
        }
      }

      console.log(`Profile created successfully with role: ${data.role}`);
      dispatch({ type: 'LOGIN', payload: result.user });
      setSuccess(true);
      reset();
      setStep('form');
      setChildren([]);
      setGuardianData(null);
      // Redirect to the complete profile page instead of dashboard
      navigate('/complete-profile');
    }
  };

  const handleGuardianComplete = async () => {
    if (!guardianData) return;

    if (children.length === 0) {
      setErrorMsg('Please add at least one child to continue.');
      return;
    }

    // Validate all children have required consents
    const missingConsents = children.some(child =>
      !child.consentSocialMedia || !child.consentAssumptionRisk
    );

    if (missingConsents) {
      setErrorMsg('Please provide all required consents for each child.');
      return;
    }

    await completeSignUp(guardianData, children);
  };  return (
    <PageCenter>
      {step === 'form' && (
        <>
          <div className="flex flex-col items-center mb-6">
            <UserPlus className="w-10 h-10 text-indigo-600 mb-2" />
            <h2 className="text-3xl font-extrabold text-indigo-700">Create your account</h2>
          </div>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block mb-1 font-medium text-gray-700">First Name</label>
              <input
                type="text"
                {...register('firstName')}
                className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                autoComplete="given-name"
              />
              {errors.firstName && <p className="text-red-600 text-sm mt-1">{errors.firstName.message}</p>}
            </div>
            <div>
              <label className="block mb-1 font-medium text-gray-700">Last Name</label>
              <input
                type="text"
                {...register('lastName')}
                className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                autoComplete="family-name"
              />
              {errors.lastName && <p className="text-red-600 text-sm mt-1">{errors.lastName.message}</p>}
            </div>
          </div>
          <div>
            <label className="block mb-1 font-medium text-gray-700">Email</label>
            <input
              type="email"
              {...register('email')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              autoComplete="email"
            />
            {errors.email && <p className="text-red-600 text-sm mt-1">{errors.email.message}</p>}
          </div>
          <div>
            <label className="block mb-1 font-medium text-gray-700">Password</label>
            <input
              type="password"
              {...register('password')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              autoComplete="new-password"
            />
            {/* Password strength indicator */}
            <div className="mt-1 h-1.5 w-full bg-gray-200 rounded-full overflow-hidden">
              <div 
                className={`h-full transition-all ${
                  passwordStrength < 30 ? 'bg-red-500' : 
                  passwordStrength < 60 ? 'bg-yellow-500' : 
                  'bg-green-500'
                }`} 
                style={{ width: `${passwordStrength}%` }}
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {passwordStrength < 30 ? 'Weak password' : 
               passwordStrength < 60 ? 'Moderate password' : 
               'Strong password'}
            </p>
            {errors.password && <p className="text-red-600 text-sm mt-1">{errors.password.message}</p>}
          </div>
          <div>
            <label className="block mb-1 font-medium text-gray-700">Confirm Password</label>
            <input
              type="password"
              {...register('confirmPassword')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              autoComplete="new-password"
            />
            {errors.confirmPassword && <p className="text-red-600 text-sm mt-1">{errors.confirmPassword.message}</p>}          </div>
          <div>
            <label className="block mb-2 font-medium text-gray-700">Sign up as</label>
            <Controller
              name="role"
              control={control}
              render={({ field }) => (
                <div className="flex space-x-3">
                  {roles.map((role) => (
                    <button
                      type="button"
                      key={role.value}
                      className={`flex items-center px-4 py-2 rounded-lg border-2 transition-all duration-150 font-semibold text-gray-700 focus:outline-none ${
                        field.value === role.value
                          ? 'border-indigo-600 bg-indigo-50 text-indigo-700'
                          : 'border-gray-200 bg-gray-50 hover:border-indigo-400'
                      }`}
                      onClick={() => field.onChange(role.value)}
                    >
                      {role.icon}
                      {role.label}
                    </button>
                  ))}
                </div>
              )}
            />
            {errors.role && <p className="text-red-600 text-sm mt-1">{errors.role.message}</p>}
          </div>

          {/* Student Date of Birth */}
          {selectedRole === 'student' && (
            <div>
              <label className="block mb-1 font-medium text-gray-700">Date of Birth *</label>
              <input
                type="date"
                {...register('dateOfBirth', { required: selectedRole === 'student' ? 'Date of birth is required for students' : false })}
                className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              />
              {errors.dateOfBirth && <p className="text-red-600 text-sm mt-1">{errors.dateOfBirth.message}</p>}
              <p className="text-xs text-gray-500 mt-1">Students must be 16 or older to register independently</p>
            </div>
          )}

          {/* Guardian Additional Fields */}
          {selectedRole === 'guardian' && (
            <>
              <div>
                <label className="block mb-1 font-medium text-gray-700">Phone Number *</label>
                <input
                  type="tel"
                  {...register('phone', { required: selectedRole === 'guardian' ? 'Phone number is required for guardians' : false })}
                  className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                  placeholder="(*************"
                />
                {errors.phone && <p className="text-red-600 text-sm mt-1">{errors.phone.message}</p>}
              </div>

              <div>
                <label className="block mb-1 font-medium text-gray-700">Address *</label>
                <textarea
                  {...register('address', { required: selectedRole === 'guardian' ? 'Address is required for guardians' : false })}
                  className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                  rows={3}
                  placeholder="Street address, city, state, zip code"
                />
                {errors.address && <p className="text-red-600 text-sm mt-1">{errors.address.message}</p>}
              </div>
            </>
          )}
          
          {!showInstructor && (
            <div>
              <label className="block mb-2 font-medium text-gray-700">Have an instructor code?</label>
              <div className="relative">
                <Key className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  {...register('instructorCode')}
                  placeholder="Enter code to sign up as instructor"
                  className="w-full pl-10 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                />
              </div>
              {instructorCodeValue && instructorCodeValue !== INSTRUCTOR_CODE && (
                <p className="text-red-600 text-sm mt-1">Invalid instructor code</p>
              )}
              {errors.instructorCode && <p className="text-red-600 text-sm mt-1">{errors.instructorCode.message}</p>}
            </div>
          )}

          {showInstructor && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <ShieldCheck className="h-5 w-5 text-green-500 mr-2" />
                <p className="text-green-700 font-medium">Valid instructor code - You're signing up as an Instructor</p>
              </div>
              <button
                type="button"
                onClick={() => {
                  setValue('instructorCode', '');
                  setValue('role', 'student');
                }}
                className="mt-2 text-sm text-green-600 hover:text-green-800 underline"
              >
                Clear code and choose different role
              </button>
            </div>
          )}

          <button
            type="submit"
            className="w-full bg-gradient-to-r from-indigo-600 to-blue-500 text-white py-2.5 rounded-lg font-bold text-lg shadow-md hover:from-indigo-700 hover:to-blue-600 transition"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Signing up...' : selectedRole === 'guardian' ? 'Continue to Add Children' : 'Sign Up'}
          </button>

          <div className="text-center mt-4">
            <p className="text-gray-600">
              Already have an account?{' '}
              <a href="/signin" className="text-indigo-600 hover:text-indigo-800 font-medium">
                Sign in
              </a>
            </p>
          </div>
        </form>
        {errorMsg && <p className="text-red-600 text-center mt-4">{errorMsg}</p>}
        {success && (
          <p className="text-green-600 text-center mt-4">
            Sign up successful! Please check your email to verify your account.
          </p>
        )}
        </>
      )}

      {/* Children Step for Guardians */}
      {step === 'children' && guardianData && (
        <>
          <div className="flex flex-col items-center mb-6">
            <Users className="w-10 h-10 text-indigo-600 mb-2" />
            <h2 className="text-3xl font-extrabold text-indigo-700">Add Your Children</h2>
            <p className="text-gray-600 mt-2">Add details for each child you want to register</p>
          </div>

          <div className="w-full max-w-4xl">
            <ChildForm
              children={children}
              onChange={setChildren}
            />

            <div className="flex justify-between mt-8">
              <button
                onClick={() => setStep('form')}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Account Details
              </button>

              <button
                onClick={handleGuardianComplete}
                disabled={children.length === 0 || isSubmitting}
                className="inline-flex items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Creating Account...' : 'Complete Registration'}
              </button>
            </div>

            {errorMsg && <p className="text-red-600 text-center mt-4">{errorMsg}</p>}
            {success && (
              <p className="text-green-600 text-center mt-4">
                Registration successful! Please check your email to verify your account.
              </p>
            )}
          </div>
        </>
      )}
    </PageCenter>
  );
};