import React, { useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { signUp, getErrorMessage, createInitialProfile } from '../services/authService';
import { useAuth } from '../contexts/AuthContext';
import { UserPlus, GraduationCap, ShieldCheck } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { PageCenter } from '../components/PageLayout';

const roles = [
  { value: 'instructor', label: 'Instructor', icon: <ShieldCheck className="w-5 h-5 mr-2" /> },
  { value: 'student', label: 'Student', icon: <GraduationCap className="w-5 h-5 mr-2" /> },
  { value: 'guardian', label: 'Guardian', icon: <UserPlus className="w-5 h-5 mr-2" /> },
];

const schema = z.object({
  email: z.string().email('Invalid email'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string(),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  role: z.enum(['instructor', 'student', 'guardian'], { required_error: 'Role is required' }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type SignUpFormData = z.infer<typeof schema>;

export const SignUpPage: React.FC = () => {
  const { dispatch } = useAuth();
  const [success, setSuccess] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    setError,
    reset,
    watch,
  } = useForm<SignUpFormData>({ resolver: zodResolver(schema), defaultValues: { role: 'student' } });
  
  const password = watch('password');
  const [passwordStrength, setPasswordStrength] = useState(0);
  // Password strength calculation
  React.useEffect(() => {
    if (password) {
      let strength = 0;
      // Length check
      if (password.length >= 6) strength += 20;
      if (password.length >= 10) strength += 10;
      // Character variety check
      if (/[A-Z]/.test(password)) strength += 20; // Uppercase
      if (/[a-z]/.test(password)) strength += 10; // Lowercase
      if (/[0-9]/.test(password)) strength += 20; // Numbers
      if (/[^A-Za-z0-9]/.test(password)) strength += 20; // Special characters
      setPasswordStrength(strength);
    } else {
      setPasswordStrength(0);
    }
  }, [password]);
    const onSubmit = async (data: SignUpFormData) => {
    setErrorMsg('');
    setSuccess(false);
    const { error, data: result } = await signUp(data.email, data.password);
    if (error) {
      setError('email', { message: getErrorMessage(error) });
      setErrorMsg(getErrorMessage(error));
      return;
    }    if (result?.user) {
      // Use our robust profile creation function with retry mechanism
      const { error: profileError } = await createInitialProfile(
        result.user.id,
        data.firstName,
        data.lastName,
        data.role
      );
      
      if (profileError) {
        console.error('Profile creation failed completely:', profileError);
        setErrorMsg('Sign up succeeded, but failed to create profile. Please contact support or try signing in.');
        return;
      }
      dispatch({ type: 'LOGIN', payload: result.user });
      setSuccess(true);
      reset();
      // Redirect to the complete profile page instead of dashboard
      navigate('/complete-profile');
    }
  };  return (
    <PageCenter>
        <div className="flex flex-col items-center mb-6">
          <UserPlus className="w-10 h-10 text-indigo-600 mb-2" />
          <h2 className="text-3xl font-extrabold text-indigo-700">Create your account</h2>
        </div>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block mb-1 font-medium text-gray-700">First Name</label>
              <input
                type="text"
                {...register('firstName')}
                className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                autoComplete="given-name"
              />
              {errors.firstName && <p className="text-red-600 text-sm mt-1">{errors.firstName.message}</p>}
            </div>
            <div>
              <label className="block mb-1 font-medium text-gray-700">Last Name</label>
              <input
                type="text"
                {...register('lastName')}
                className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                autoComplete="family-name"
              />
              {errors.lastName && <p className="text-red-600 text-sm mt-1">{errors.lastName.message}</p>}
            </div>
          </div>
          <div>
            <label className="block mb-1 font-medium text-gray-700">Email</label>
            <input
              type="email"
              {...register('email')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              autoComplete="email"
            />
            {errors.email && <p className="text-red-600 text-sm mt-1">{errors.email.message}</p>}
          </div>
          <div>
            <label className="block mb-1 font-medium text-gray-700">Password</label>
            <input
              type="password"
              {...register('password')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              autoComplete="new-password"
            />
            {/* Password strength indicator */}
            <div className="mt-1 h-1.5 w-full bg-gray-200 rounded-full overflow-hidden">
              <div 
                className={`h-full transition-all ${
                  passwordStrength < 30 ? 'bg-red-500' : 
                  passwordStrength < 60 ? 'bg-yellow-500' : 
                  'bg-green-500'
                }`} 
                style={{ width: `${passwordStrength}%` }}
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {passwordStrength < 30 ? 'Weak password' : 
               passwordStrength < 60 ? 'Moderate password' : 
               'Strong password'}
            </p>
            {errors.password && <p className="text-red-600 text-sm mt-1">{errors.password.message}</p>}
          </div>
          <div>
            <label className="block mb-1 font-medium text-gray-700">Confirm Password</label>
            <input
              type="password"
              {...register('confirmPassword')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              autoComplete="new-password"
            />
            {errors.confirmPassword && <p className="text-red-600 text-sm mt-1">{errors.confirmPassword.message}</p>}
          </div>
          <div>
            <label className="block mb-2 font-medium text-gray-700">Sign up as</label>
            <Controller
              name="role"
              control={control}
              render={({ field }) => (
                <div className="flex space-x-3">
                  {roles.map((role) => (
                    <button
                      type="button"
                      key={role.value}
                      className={`flex items-center px-4 py-2 rounded-lg border-2 transition-all duration-150 font-semibold text-gray-700 focus:outline-none ${
                        field.value === role.value
                          ? 'border-indigo-600 bg-indigo-50 text-indigo-700'
                          : 'border-gray-200 bg-gray-50 hover:border-indigo-400'
                      }`}
                      onClick={() => field.onChange(role.value)}
                    >
                      {role.icon}
                      {role.label}
                    </button>
                  ))}
                </div>
              )}
            />
            {errors.role && <p className="text-red-600 text-sm mt-1">{errors.role.message}</p>}
          </div>          <button
            type="submit"
            className="w-full bg-gradient-to-r from-indigo-600 to-blue-500 text-white py-2.5 rounded-lg font-bold text-lg shadow-md hover:from-indigo-700 hover:to-blue-600 transition"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Signing up...' : 'Sign Up'}
          </button>
          
          <div className="text-center mt-4">
            <p className="text-gray-600">
              Already have an account?{' '}
              <a href="/signin" className="text-indigo-600 hover:text-indigo-800 font-medium">
                Sign in
              </a>
            </p>
          </div>
        </form>
        {errorMsg && <p className="text-red-600 text-center mt-4">{errorMsg}</p>}
        {success && (
          <p className="text-green-600 text-center mt-4">
            Sign up successful! Please check your email to verify your account.
          </p>        )}
    </PageCenter>
  );
};