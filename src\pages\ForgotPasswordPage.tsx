import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { supabase } from '../services/supabaseClient';
import { MailCheck } from 'lucide-react';
import { Link } from 'react-router-dom';
import { PageCenter } from '../components/PageLayout';

const schema = z.object({
  email: z.string().email('Invalid email'),
});

type ForgotPasswordFormData = z.infer<typeof schema>;

export const ForgotPasswordPage: React.FC = () => {
  const [success, setSuccess] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
    reset,
  } = useForm<ForgotPasswordFormData>({ resolver: zodResolver(schema) });

  const onSubmit = async (data: ForgotPasswordFormData) => {
    setErrorMsg('');
    setSuccess(false);
    const { error } = await supabase.auth.resetPasswordForEmail(data.email, {
      redirectTo: window.location.origin + '/reset-password',
    });
    if (error) {
      setError('email', { message: error.message });
      setErrorMsg(error.message);
      return;
    }
    setSuccess(true);
    reset();
  };

  return (
    <PageCenter>
        <div className="flex flex-col items-center mb-6">
          <MailCheck className="w-10 h-10 text-indigo-600 mb-2" />
          <h2 className="text-3xl font-extrabold text-indigo-700">Forgot Password</h2>
        </div>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div>
            <label className="block mb-1 font-medium text-gray-700">Email</label>
            <input
              type="email"
              {...register('email')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              autoComplete="email"
            />
            {errors.email && <p className="text-red-600 text-sm mt-1">{errors.email.message}</p>}
          </div>
          <button
            type="submit"
            className="w-full bg-gradient-to-r from-indigo-600 to-blue-500 text-white py-2.5 rounded-lg font-bold text-lg shadow-md hover:from-indigo-700 hover:to-blue-600 transition"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Sending...' : 'Send Reset Link'}
          </button>
        </form>
        {errorMsg && <p className="text-red-600 text-center mt-4">{errorMsg}</p>}
        {success && (
          <p className="text-green-600 text-center mt-4">
            If an account exists for that email, a reset link has been sent.
          </p>
        )}
        <div className="text-center mt-6">
          <p className="text-gray-600">
            Remember your password?{' '}
            <Link to="/signin" className="text-indigo-600 hover:text-indigo-800 font-medium">
              Back to Sign in
            </Link>
          </p>
        </div>
    </PageCenter>
  );
};
