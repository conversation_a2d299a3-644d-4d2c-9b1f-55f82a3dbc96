import React, { useState, useEffect } from 'react';
import { Search, Plus, Filter, Users, Eye, Edit, Trash2 } from 'lucide-react';
import { studentService } from '../services';
import type { Student, StudentFilters, BELT_COLORS } from '../types';
import { useAuth } from '../contexts/AuthContext';

interface StudentListProps {
  onSelectStudent?: (student: Student) => void;
  onEditStudent?: (student: Student) => void;
  onCreateStudent?: () => void;
  showActions?: boolean;
  filters?: StudentFilters;
}

export const StudentList: React.FC<StudentListProps> = ({
  onSelectStudent,
  onEditStudent,
  onCreateStudent,
  showActions = true,
  filters: externalFilters
}) => {
  const { state } = useAuth();
  const { profile } = state;
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<StudentFilters>({
    search: '',
    belt_color: '',
    active: true,
    ...externalFilters
  });
  const [showFilters, setShowFilters] = useState(false);

  const beltColors = ['white', 'yellow', 'orange', 'green', 'blue', 'purple', 'brown', 'black'];

  const loadStudents = async () => {
    setLoading(true);
    setError(null);

    try {
      let result;
      
      // Role-based data fetching
      if (profile?.role === 'instructor' || profile?.role === 'admin') {
        result = await studentService.getStudents(filters);
      } else if (profile?.role === 'guardian' || profile?.role === 'student') {
        result = await studentService.getStudentsByUser(profile.id);
      } else {
        setError('Unauthorized access');
        return;
      }

      if (result.error) {
        setError(result.error);
      } else {
        setStudents(result.data);
      }
    } catch (err) {
      setError('Failed to load students');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadStudents();
  }, [filters, profile]);

  const handleSearch = (searchTerm: string) => {
    setFilters(prev => ({ ...prev, search: searchTerm }));
  };

  const handleFilterChange = (key: keyof StudentFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleDeleteStudent = async (student: Student) => {
    if (!confirm(`Are you sure you want to deactivate ${student.first_name} ${student.last_name}?`)) {
      return;
    }

    const result = await studentService.deleteStudent(student.id);
    if (result.error) {
      setError(result.error);
    } else {
      loadStudents(); // Reload the list
    }
  };

  const getBeltColorBadge = (beltColor: string) => {
    const colorMap: Record<string, string> = {
      white: 'bg-gray-100 text-gray-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      orange: 'bg-orange-100 text-orange-800',
      green: 'bg-green-100 text-green-800',
      blue: 'bg-blue-100 text-blue-800',
      purple: 'bg-purple-100 text-purple-800',
      brown: 'bg-amber-100 text-amber-800',
      black: 'bg-gray-800 text-white'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorMap[beltColor] || 'bg-gray-100 text-gray-800'}`}>
        {beltColor.charAt(0).toUpperCase() + beltColor.slice(1)} Belt
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-2">
          <Users className="h-6 w-6 text-indigo-600" />
          <h2 className="text-2xl font-bold text-gray-900">Students</h2>
          <span className="bg-indigo-100 text-indigo-800 text-sm font-medium px-2.5 py-0.5 rounded-full">
            {students.length}
          </span>
        </div>
        
        {showActions && (profile?.role === 'instructor' || profile?.role === 'admin') && onCreateStudent && (
          <button
            onClick={onCreateStudent}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Student
          </button>
        )}
      </div>

      {/* Search and Filters */}
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search students..."
              value={filters.search || ''}
              onChange={(e) => handleSearch(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
          
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>

        {showFilters && (
          <div className="bg-gray-50 p-4 rounded-lg space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Belt Color
                </label>
                <select
                  value={filters.belt_color || ''}
                  onChange={(e) => handleFilterChange('belt_color', e.target.value || undefined)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">All Belts</option>
                  {beltColors.map(color => (
                    <option key={color} value={color}>
                      {color.charAt(0).toUpperCase() + color.slice(1)} Belt
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={filters.active === undefined ? '' : filters.active.toString()}
                  onChange={(e) => handleFilterChange('active', e.target.value === '' ? undefined : e.target.value === 'true')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">All Students</option>
                  <option value="true">Active</option>
                  <option value="false">Inactive</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-600">{error}</div>
        </div>
      )}

      {/* Students List */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        {students.length === 0 ? (
          <div className="text-center py-12">
            <Users className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No students found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filters.search ? 'Try adjusting your search or filters.' : 'Get started by adding a new student.'}
            </p>
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {students.map((student) => (
              <li key={student.id} className="px-6 py-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                        <span className="text-sm font-medium text-indigo-600">
                          {student.first_name[0]}{student.last_name[0]}
                        </span>
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {student.first_name} {student.last_name}
                        </p>
                        {getBeltColorBadge(student.belt_color)}
                        {!student.active && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Inactive
                          </span>
                        )}
                      </div>
                      <div className="flex items-center space-x-4 mt-1">
                        <p className="text-sm text-gray-500">
                          Age: {new Date().getFullYear() - new Date(student.date_of_birth).getFullYear()}
                        </p>
                        {student.guardian && (
                          <p className="text-sm text-gray-500">
                            Guardian: {student.guardian.first_name} {student.guardian.last_name}
                          </p>
                        )}
                        {student.last_graded && (
                          <p className="text-sm text-gray-500">
                            Last graded: {new Date(student.last_graded).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {showActions && (
                    <div className="flex items-center space-x-2">
                      {onSelectStudent && (
                        <button
                          onClick={() => onSelectStudent(student)}
                          className="text-indigo-600 hover:text-indigo-900 p-1"
                          title="View Details"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                      )}
                      {onEditStudent && (profile?.role === 'instructor' || profile?.role === 'admin') && (
                        <button
                          onClick={() => onEditStudent(student)}
                          className="text-gray-600 hover:text-gray-900 p-1"
                          title="Edit Student"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                      )}
                      {(profile?.role === 'instructor' || profile?.role === 'admin') && (
                        <button
                          onClick={() => handleDeleteStudent(student)}
                          className="text-red-600 hover:text-red-900 p-1"
                          title="Deactivate Student"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};
