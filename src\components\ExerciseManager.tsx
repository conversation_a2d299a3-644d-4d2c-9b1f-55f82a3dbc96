import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Activity, Save, X, Target } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { supabase } from '../services/supabaseClient';
import { useAuth } from '../contexts/AuthContext';

interface Exercise {
  id: string;
  name: string;
  description?: string;
  exercise_type: 'cardio' | 'strength' | 'flexibility' | 'sports' | 'martial_arts';
  instructions?: string;
  muscle_groups: string[];
  equipment_needed: string[];
  difficulty_level: number;
  is_active: boolean;
  created_at: string;
}

interface ExerciseForm {
  name: string;
  description: string;
  exercise_type: 'cardio' | 'strength' | 'flexibility' | 'sports' | 'martial_arts';
  instructions: string;
  muscle_groups: string;
  equipment_needed: string;
  difficulty_level: number;
  is_active: boolean;
}

const EXERCISE_TYPES = [
  { value: 'cardio', label: 'Cardio' },
  { value: 'strength', label: 'Strength' },
  { value: 'flexibility', label: 'Flexibility' },
  { value: 'sports', label: 'Sports' },
  { value: 'martial_arts', label: 'Martial Arts' }
];

export const ExerciseManager: React.FC = () => {
  const { state } = useAuth();
  const { profile } = state;
  const [exercises, setExercises] = useState<Exercise[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingExercise, setEditingExercise] = useState<Exercise | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [filterType, setFilterType] = useState<string>('all');

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<ExerciseForm>();

  useEffect(() => {
    loadExercises();
  }, []);

  const loadExercises = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('exercises')
        .select('*')
        .order('name');

      if (error) {
        setError(error.message);
      } else {
        setExercises(data || []);
      }
    } catch (err) {
      setError('Failed to load exercises');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: ExerciseForm) => {
    if (!profile) return;

    setError(null);

    try {
      const exerciseData = {
        ...data,
        muscle_groups: data.muscle_groups.split(',').map(g => g.trim()).filter(g => g),
        equipment_needed: data.equipment_needed.split(',').map(e => e.trim()).filter(e => e),
        created_by: profile.id
      };

      if (editingExercise) {
        // Update existing exercise
        const { error } = await supabase
          .from('exercises')
          .update(exerciseData)
          .eq('id', editingExercise.id);

        if (error) {
          setError(error.message);
          return;
        }
      } else {
        // Create new exercise
        const { error } = await supabase
          .from('exercises')
          .insert([exerciseData]);

        if (error) {
          setError(error.message);
          return;
        }
      }

      // Reload data and close form
      await loadExercises();
      setShowForm(false);
      setEditingExercise(null);
      reset();
    } catch (err) {
      setError('Failed to save exercise');
    }
  };

  const handleEdit = (exercise: Exercise) => {
    setEditingExercise(exercise);
    reset({
      name: exercise.name,
      description: exercise.description || '',
      exercise_type: exercise.exercise_type,
      instructions: exercise.instructions || '',
      muscle_groups: exercise.muscle_groups.join(', '),
      equipment_needed: exercise.equipment_needed.join(', '),
      difficulty_level: exercise.difficulty_level,
      is_active: exercise.is_active
    });
    setShowForm(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this exercise?')) return;

    try {
      const { error } = await supabase
        .from('exercises')
        .delete()
        .eq('id', id);

      if (error) {
        setError(error.message);
      } else {
        await loadExercises();
      }
    } catch (err) {
      setError('Failed to delete exercise');
    }
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingExercise(null);
    reset();
  };

  const filteredExercises = exercises.filter(exercise => {
    if (filterType === 'all') return true;
    return exercise.exercise_type === filterType;
  });

  const getDifficultyColor = (level: number) => {
    switch (level) {
      case 1: return 'bg-green-100 text-green-800';
      case 2: return 'bg-blue-100 text-blue-800';
      case 3: return 'bg-yellow-100 text-yellow-800';
      case 4: return 'bg-orange-100 text-orange-800';
      case 5: return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <Activity className="h-6 w-6 text-indigo-600 mr-3" />
              Exercise Management
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Create and manage exercises for fitness tracking
            </p>
          </div>
          
          <button
            onClick={() => setShowForm(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Exercise
          </button>
        </div>

        {/* Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Filter by Type
          </label>
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="block w-full max-w-xs px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="all">All Exercise Types</option>
            {EXERCISE_TYPES.map(type => (
              <option key={type.value} value={type.value}>{type.label}</option>
            ))}
          </select>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-600">{error}</div>
        </div>
      )}

      {/* Form */}
      {showForm && (
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {editingExercise ? 'Edit Exercise' : 'Add New Exercise'}
          </h3>
          
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Exercise Name *
                </label>
                <input
                  type="text"
                  {...register('name', { required: 'Exercise name is required' })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="e.g., Push-ups, Running"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Exercise Type *
                </label>
                <select
                  {...register('exercise_type', { required: 'Exercise type is required' })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">Select Type</option>
                  {EXERCISE_TYPES.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
                {errors.exercise_type && (
                  <p className="mt-1 text-sm text-red-600">{errors.exercise_type.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                {...register('description')}
                rows={3}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Brief description of the exercise"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Instructions
              </label>
              <textarea
                {...register('instructions')}
                rows={4}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Step-by-step instructions for performing the exercise"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Muscle Groups
                </label>
                <input
                  type="text"
                  {...register('muscle_groups')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="e.g., chest, triceps, shoulders (comma-separated)"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Equipment Needed
                </label>
                <input
                  type="text"
                  {...register('equipment_needed')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="e.g., dumbbells, mat (comma-separated)"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Difficulty Level (1-5) *
                </label>
                <input
                  type="number"
                  min="1"
                  max="5"
                  {...register('difficulty_level', { 
                    required: 'Difficulty level is required',
                    min: { value: 1, message: 'Minimum difficulty is 1' },
                    max: { value: 5, message: 'Maximum difficulty is 5' }
                  })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                />
                {errors.difficulty_level && (
                  <p className="mt-1 text-sm text-red-600">{errors.difficulty_level.message}</p>
                )}
              </div>

              <div className="flex items-center pt-6">
                <input
                  type="checkbox"
                  {...register('is_active')}
                  defaultChecked={true}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-900">
                  Active (available for use)
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={handleCancel}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <Save className="h-4 w-4 mr-2" />
                {editingExercise ? 'Update' : 'Create'} Exercise
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Exercise List */}
      <div className="bg-white shadow rounded-lg">
        {filteredExercises.length === 0 ? (
          <div className="p-12 text-center">
            <Activity className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No exercises found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filterType === 'all' 
                ? 'Get started by creating your first exercise.'
                : `No ${EXERCISE_TYPES.find(t => t.value === filterType)?.label.toLowerCase()} exercises found.`
              }
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredExercises.map((exercise) => (
              <div key={exercise.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-medium text-gray-900">{exercise.name}</h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getDifficultyColor(exercise.difficulty_level)}`}>
                        Level {exercise.difficulty_level}
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                        {EXERCISE_TYPES.find(t => t.value === exercise.exercise_type)?.label}
                      </span>
                      {!exercise.is_active && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          Inactive
                        </span>
                      )}
                    </div>
                    
                    {exercise.description && (
                      <p className="text-sm text-gray-600 mb-2">{exercise.description}</p>
                    )}
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-500">
                      {exercise.muscle_groups.length > 0 && (
                        <div>
                          <strong>Muscle Groups:</strong> {exercise.muscle_groups.join(', ')}
                        </div>
                      )}
                      {exercise.equipment_needed.length > 0 && (
                        <div>
                          <strong>Equipment:</strong> {exercise.equipment_needed.join(', ')}
                        </div>
                      )}
                    </div>
                    
                    {exercise.instructions && (
                      <div className="mt-3 text-sm text-gray-600">
                        <strong>Instructions:</strong> {exercise.instructions}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => handleEdit(exercise)}
                      className="text-indigo-600 hover:text-indigo-900 p-1"
                      title="Edit"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(exercise.id)}
                      className="text-red-600 hover:text-red-900 p-1"
                      title="Delete"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
