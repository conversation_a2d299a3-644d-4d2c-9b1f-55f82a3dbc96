import { supabase } from './supabaseClient';
import type { 
  Grading, 
  Syllabus,
  RecordGradingForm, 
  ApiResponse, 
  PaginatedResponse 
} from '../types';

export const gradingService = {
  // Get all syllabus items
  async getSyllabus(): Promise<PaginatedResponse<Syllabus>> {
    try {
      const { data, error, count } = await supabase
        .from('syllabus')
        .select('*')
        .order('belt_color')
        .order('order_index');

      if (error) {
        return { data: [], count: 0, error: error.message };
      }

      return { data: data || [], count: count || 0, error: null };
    } catch (error) {
      return { data: [], count: 0, error: 'Failed to fetch syllabus' };
    }
  },

  // Get syllabus for a specific belt color
  async getSyllabusByBelt(beltColor: string): Promise<PaginatedResponse<Syllabus>> {
    try {
      const { data, error, count } = await supabase
        .from('syllabus')
        .select('*')
        .eq('belt_color', beltColor)
        .order('order_index');

      if (error) {
        return { data: [], count: 0, error: error.message };
      }

      return { data: data || [], count: count || 0, error: null };
    } catch (error) {
      return { data: [], count: 0, error: 'Failed to fetch syllabus for belt' };
    }
  },

  // Get gradings for a student
  async getStudentGradings(studentId: string): Promise<PaginatedResponse<Grading>> {
    try {
      const { data, error, count } = await supabase
        .from('gradings')
        .select(`
          *,
          student:student_id(id, first_name, last_name, belt_color),
          syllabus:syllabus_id(*),
          grader:graded_by(id, first_name, last_name, role)
        `)
        .eq('student_id', studentId)
        .order('graded_at', { ascending: false });

      if (error) {
        return { data: [], count: 0, error: error.message };
      }

      return { data: data || [], count: count || 0, error: null };
    } catch (error) {
      return { data: [], count: 0, error: 'Failed to fetch student gradings' };
    }
  },

  // Record a grading
  async recordGrading(gradingData: RecordGradingForm, gradedBy: string): Promise<ApiResponse<Grading>> {
    try {
      const { data, error } = await supabase
        .from('gradings')
        .upsert([{
          ...gradingData,
          graded_by: gradedBy
        }], {
          onConflict: 'student_id,syllabus_id'
        })
        .select(`
          *,
          student:student_id(id, first_name, last_name, belt_color),
          syllabus:syllabus_id(*),
          grader:graded_by(id, first_name, last_name, role)
        `)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to record grading' };
    }
  },

  // Get grading progress for a student and belt
  async getGradingProgress(studentId: string, beltColor: string): Promise<ApiResponse<{
    totalSkills: number;
    passedSkills: number;
    failedSkills: number;
    notAssessedSkills: number;
    progressPercentage: number;
    skillsBreakdown: Array<{
      category: string;
      total: number;
      passed: number;
      failed: number;
      notAssessed: number;
    }>;
  }>> {
    try {
      // Get all syllabus items for the belt
      const { data: syllabusItems, error: syllabusError } = await supabase
        .from('syllabus')
        .select('*')
        .eq('belt_color', beltColor);

      if (syllabusError) {
        return { data: null, error: syllabusError.message };
      }

      // Get all gradings for the student
      const { data: gradings, error: gradingsError } = await supabase
        .from('gradings')
        .select(`
          *,
          syllabus:syllabus_id(*)
        `)
        .eq('student_id', studentId);

      if (gradingsError) {
        return { data: null, error: gradingsError.message };
      }

      const totalSkills = syllabusItems?.length || 0;
      const gradingMap = new Map(gradings?.map(g => [g.syllabus_id, g.result]) || []);

      let passedSkills = 0;
      let failedSkills = 0;
      let notAssessedSkills = 0;

      // Group by category
      const categoryMap = new Map<string, { total: number; passed: number; failed: number; notAssessed: number }>();

      syllabusItems?.forEach(skill => {
        const result = gradingMap.get(skill.id) || 'not_assessed';
        
        if (result === 'pass') passedSkills++;
        else if (result === 'fail') failedSkills++;
        else notAssessedSkills++;

        // Update category stats
        if (!categoryMap.has(skill.category)) {
          categoryMap.set(skill.category, { total: 0, passed: 0, failed: 0, notAssessed: 0 });
        }
        const categoryStats = categoryMap.get(skill.category)!;
        categoryStats.total++;
        if (result === 'pass') categoryStats.passed++;
        else if (result === 'fail') categoryStats.failed++;
        else categoryStats.notAssessed++;
      });

      const progressPercentage = totalSkills > 0 ? (passedSkills / totalSkills) * 100 : 0;

      const skillsBreakdown = Array.from(categoryMap.entries()).map(([category, stats]) => ({
        category,
        ...stats
      }));

      return {
        data: {
          totalSkills,
          passedSkills,
          failedSkills,
          notAssessedSkills,
          progressPercentage,
          skillsBreakdown
        },
        error: null
      };
    } catch (error) {
      return { data: null, error: 'Failed to get grading progress' };
    }
  }
};
