-- Create medical conditions lookup table
CREATE TABLE medical_condition_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE medical_conditions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    category_id UUID REFERENCES medical_condition_categories(id),
    name TEXT NOT NULL,
    description TEXT,
    severity_level TEXT CHECK (severity_level IN ('mild', 'moderate', 'severe')),
    requires_attention BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create student medical conditions junction table
CREATE TABLE student_medical_conditions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    student_id UUID REFERENCES students(id) ON DELETE CASCADE,
    medical_condition_id UUID REFERENCES medical_conditions(id),
    notes TEXT,
    severity TEXT,
    medication TEXT,
    emergency_contact_aware BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, medical_condition_id)
);

-- Insert medical condition categories
INSERT INTO medical_condition_categories (name, description, display_order) VALUES
('Respiratory', 'Breathing and lung-related conditions', 1),
('Cardiovascular', 'Heart and circulation conditions', 2),
('Musculoskeletal', 'Bone, joint, and muscle conditions', 3),
('Neurological', 'Brain and nervous system conditions', 4),
('Metabolic', 'Metabolism and endocrine conditions', 5),
('Allergies', 'Allergic reactions and sensitivities', 6),
('Mental Health', 'Psychological and mental health conditions', 7),
('Other', 'Other medical conditions', 8);

-- Insert common medical conditions
INSERT INTO medical_conditions (category_id, name, description, severity_level, requires_attention, display_order) 
SELECT 
    c.id,
    condition_name,
    condition_desc,
    severity,
    attention_required,
    row_number() OVER (PARTITION BY c.id ORDER BY condition_name)
FROM medical_condition_categories c
CROSS JOIN (
    VALUES 
    -- Respiratory
    ('Respiratory', 'Asthma', 'Chronic respiratory condition affecting breathing', 'moderate', true),
    ('Respiratory', 'Exercise-induced asthma', 'Asthma triggered by physical activity', 'moderate', true),
    ('Respiratory', 'Chronic bronchitis', 'Long-term inflammation of bronchial tubes', 'moderate', true),
    ('Respiratory', 'Allergic rhinitis', 'Hay fever and seasonal allergies', 'mild', false),
    
    -- Cardiovascular
    ('Cardiovascular', 'Heart murmur', 'Abnormal heart sound', 'mild', true),
    ('Cardiovascular', 'High blood pressure', 'Elevated blood pressure', 'moderate', true),
    ('Cardiovascular', 'Arrhythmia', 'Irregular heartbeat', 'moderate', true),
    ('Cardiovascular', 'Previous heart surgery', 'History of cardiac procedures', 'severe', true),
    
    -- Musculoskeletal
    ('Musculoskeletal', 'Previous fracture', 'History of broken bones', 'mild', false),
    ('Musculoskeletal', 'Joint problems', 'Issues with joints (knee, ankle, etc.)', 'moderate', false),
    ('Musculoskeletal', 'Back problems', 'Chronic back pain or injury', 'moderate', false),
    ('Musculoskeletal', 'Arthritis', 'Joint inflammation', 'moderate', true),
    ('Musculoskeletal', 'Scoliosis', 'Curvature of the spine', 'moderate', false),
    
    -- Neurological
    ('Neurological', 'Epilepsy', 'Seizure disorder', 'severe', true),
    ('Neurological', 'Migraine', 'Severe headaches', 'moderate', false),
    ('Neurological', 'Concussion history', 'Previous head injuries', 'moderate', true),
    ('Neurological', 'ADHD', 'Attention deficit hyperactivity disorder', 'mild', false),
    
    -- Metabolic
    ('Metabolic', 'Type 1 Diabetes', 'Insulin-dependent diabetes', 'severe', true),
    ('Metabolic', 'Type 2 Diabetes', 'Non-insulin dependent diabetes', 'moderate', true),
    ('Metabolic', 'Thyroid disorder', 'Thyroid gland dysfunction', 'moderate', false),
    ('Metabolic', 'Growth hormone deficiency', 'Insufficient growth hormone', 'moderate', false),
    
    -- Allergies
    ('Allergies', 'Food allergies', 'Allergic reactions to specific foods', 'moderate', true),
    ('Allergies', 'Drug allergies', 'Allergic reactions to medications', 'severe', true),
    ('Allergies', 'Latex allergy', 'Allergic reaction to latex', 'moderate', true),
    ('Allergies', 'Insect sting allergy', 'Severe reaction to bee/wasp stings', 'severe', true),
    
    -- Mental Health
    ('Mental Health', 'Anxiety disorder', 'Chronic anxiety condition', 'moderate', false),
    ('Mental Health', 'Depression', 'Clinical depression', 'moderate', false),
    ('Mental Health', 'Autism spectrum disorder', 'Developmental condition', 'mild', false),
    ('Mental Health', 'Eating disorder', 'Disordered eating patterns', 'moderate', true),
    
    -- Other
    ('Other', 'Vision problems', 'Requires glasses or contacts', 'mild', false),
    ('Other', 'Hearing problems', 'Hearing impairment', 'mild', false),
    ('Other', 'Skin conditions', 'Eczema, psoriasis, etc.', 'mild', false),
    ('Other', 'Blood disorders', 'Anemia, clotting disorders, etc.', 'moderate', true)
) AS conditions(category_name, condition_name, condition_desc, severity, attention_required)
WHERE c.name = conditions.category_name;

-- Create indexes for performance
CREATE INDEX idx_medical_conditions_category ON medical_conditions(category_id);
CREATE INDEX idx_student_medical_conditions_student ON student_medical_conditions(student_id);
CREATE INDEX idx_medical_conditions_requires_attention ON medical_conditions(requires_attention);

-- Add RLS policies (disabled for now as per previous migration)
-- These will be enabled when we re-implement RLS
-- ALTER TABLE medical_condition_categories ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE medical_conditions ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE student_medical_conditions ENABLE ROW LEVEL SECURITY;
