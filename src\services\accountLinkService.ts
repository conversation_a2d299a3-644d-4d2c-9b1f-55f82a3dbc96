import { supabase } from './supabaseClient';
import type { 
  AccountLinkRequest,
  ApiResponse, 
  PaginatedResponse 
} from '../types';

export const accountLinkService = {
  // Get all account link requests (for admins/instructors)
  async getAllRequests(): Promise<PaginatedResponse<AccountLinkRequest>> {
    try {
      const { data, error, count } = await supabase
        .from('account_link_requests')
        .select(`
          *,
          student:students!account_link_requests_student_id_fkey(id, first_name, last_name, belt_color),
          requester:profiles!account_link_requests_requested_by_fkey(id, first_name, last_name, role),
          reviewer:profiles!account_link_requests_reviewed_by_fkey(id, first_name, last_name, role)
        `)
        .order('requested_at', { ascending: false });

      if (error) {
        return { data: [], count: 0, error: error.message };
      }

      return { data: data || [], count: count || 0, error: null };
    } catch (error) {
      return { data: [], count: 0, error: 'Failed to fetch account link requests' };
    }
  },

  // Get pending requests
  async getPendingRequests(): Promise<PaginatedResponse<AccountLinkRequest>> {
    try {
      const { data, error, count } = await supabase
        .from('account_link_requests')
        .select(`
          *,
          student:students!account_link_requests_student_id_fkey(id, first_name, last_name, belt_color),
          requester:profiles!account_link_requests_requested_by_fkey(id, first_name, last_name, role),
          reviewer:profiles!account_link_requests_reviewed_by_fkey(id, first_name, last_name, role)
        `)
        .eq('status', 'pending')
        .order('requested_at', { ascending: true });

      if (error) {
        return { data: [], count: 0, error: error.message };
      }

      return { data: data || [], count: count || 0, error: null };
    } catch (error) {
      return { data: [], count: 0, error: 'Failed to fetch pending requests' };
    }
  },

  // Get requests by user
  async getUserRequests(userId: string): Promise<PaginatedResponse<AccountLinkRequest>> {
    try {
      const { data, error, count } = await supabase
        .from('account_link_requests')
        .select(`
          *,
          student:students!account_link_requests_student_id_fkey(id, first_name, last_name, belt_color),
          requester:profiles!account_link_requests_requested_by_fkey(id, first_name, last_name, role),
          reviewer:profiles!account_link_requests_reviewed_by_fkey(id, first_name, last_name, role)
        `)
        .eq('requested_by', userId)
        .order('requested_at', { ascending: false });

      if (error) {
        return { data: [], count: 0, error: error.message };
      }

      return { data: data || [], count: count || 0, error: null };
    } catch (error) {
      return { data: [], count: 0, error: 'Failed to fetch user requests' };
    }
  },

  // Create a new account link request
  async createRequest(
    studentId: string,
    userId: string,
    relationshipType: 'self' | 'guardian',
    requestMessage?: string
  ): Promise<ApiResponse<AccountLinkRequest>> {
    try {
      // Check if request already exists
      const { data: existingRequest } = await supabase
        .from('account_link_requests')
        .select('id, status')
        .eq('student_id', studentId)
        .eq('user_id', userId)
        .eq('relationship_type', relationshipType)
        .in('status', ['pending', 'approved'])
        .single();

      if (existingRequest) {
        return { 
          data: null, 
          error: existingRequest.status === 'pending' 
            ? 'A request for this student is already pending'
            : 'This account is already linked to the student'
        };
      }

      const { data, error } = await supabase
        .from('account_link_requests')
        .insert([{
          student_id: studentId,
          user_id: userId,
          relationship_type: relationshipType,
          requested_by: userId,
          request_message: requestMessage
        }])
        .select(`
          *,
          student:students!account_link_requests_student_id_fkey(id, first_name, last_name, belt_color),
          requester:profiles!account_link_requests_requested_by_fkey(id, first_name, last_name, role),
          reviewer:profiles!account_link_requests_reviewed_by_fkey(id, first_name, last_name, role)
        `)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to create account link request' };
    }
  },

  // Approve a request
  async approveRequest(
    requestId: string,
    reviewedBy: string,
    reviewNotes?: string
  ): Promise<ApiResponse<AccountLinkRequest>> {
    try {
      // Get the request details first
      const { data: request, error: fetchError } = await supabase
        .from('account_link_requests')
        .select('student_id, user_id, relationship_type')
        .eq('id', requestId)
        .single();

      if (fetchError) {
        return { data: null, error: fetchError.message };
      }

      // Start a transaction to update both the request and the student record
      const { data, error } = await supabase
        .from('account_link_requests')
        .update({
          status: 'approved',
          reviewed_by: reviewedBy,
          review_notes: reviewNotes,
          reviewed_at: new Date().toISOString()
        })
        .eq('id', requestId)
        .select(`
          *,
          student:students!account_link_requests_student_id_fkey(id, first_name, last_name, belt_color),
          requester:profiles!account_link_requests_requested_by_fkey(id, first_name, last_name, role),
          reviewer:profiles!account_link_requests_reviewed_by_fkey(id, first_name, last_name, role)
        `)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      // Update the student record to link the account
      if (request.relationship_type === 'self') {
        await supabase
          .from('students')
          .update({ user_id: request.user_id })
          .eq('id', request.student_id);
      } else if (request.relationship_type === 'guardian') {
        await supabase
          .from('students')
          .update({ guardian_id: request.user_id })
          .eq('id', request.student_id);
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to approve request' };
    }
  },

  // Reject a request
  async rejectRequest(
    requestId: string,
    reviewedBy: string,
    reviewNotes?: string
  ): Promise<ApiResponse<AccountLinkRequest>> {
    try {
      const { data, error } = await supabase
        .from('account_link_requests')
        .update({
          status: 'rejected',
          reviewed_by: reviewedBy,
          review_notes: reviewNotes,
          reviewed_at: new Date().toISOString()
        })
        .eq('id', requestId)
        .select(`
          *,
          student:students!account_link_requests_student_id_fkey(id, first_name, last_name, belt_color),
          requester:profiles!account_link_requests_requested_by_fkey(id, first_name, last_name, role),
          reviewer:profiles!account_link_requests_reviewed_by_fkey(id, first_name, last_name, role)
        `)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to reject request' };
    }
  },

  // Cancel a request (by the requester)
  async cancelRequest(requestId: string, userId: string): Promise<ApiResponse<boolean>> {
    try {
      const { error } = await supabase
        .from('account_link_requests')
        .delete()
        .eq('id', requestId)
        .eq('requested_by', userId)
        .eq('status', 'pending');

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: true, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to cancel request' };
    }
  },

  // Search for students to link (for creating requests)
  async searchStudentsForLinking(searchTerm: string): Promise<PaginatedResponse<{
    id: string;
    first_name: string;
    last_name: string;
    belt_color: string;
    date_of_birth: string;
    hasUserAccount: boolean;
    hasGuardian: boolean;
  }>> {
    try {
      const { data, error, count } = await supabase
        .from('students')
        .select('id, first_name, last_name, belt_color, date_of_birth, user_id, guardian_id')
        .or(`first_name.ilike.%${searchTerm}%,last_name.ilike.%${searchTerm}%`)
        .eq('active', true)
        .order('last_name');

      if (error) {
        return { data: [], count: 0, error: error.message };
      }

      const formattedData = data?.map(student => ({
        id: student.id,
        first_name: student.first_name,
        last_name: student.last_name,
        belt_color: student.belt_color,
        date_of_birth: student.date_of_birth,
        hasUserAccount: !!student.user_id,
        hasGuardian: !!student.guardian_id
      })) || [];

      return { data: formattedData, count: count || 0, error: null };
    } catch (error) {
      return { data: [], count: 0, error: 'Failed to search students' };
    }
  }
};
