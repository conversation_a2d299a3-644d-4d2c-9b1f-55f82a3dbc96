import React from 'react';
import type { ReactNode } from 'react';

// Props for PageCenter component
interface PageCenterProps {
  children: ReactNode;
  className?: string;
  maxWidth?: string;
}

// Full-height centered page layout with gradient background and sidebars
export const PageCenter: React.FC<PageCenterProps> = ({ 
  children, 
  className = "",
  maxWidth = "max-w-md" 
}) => {  
  return (
    <div className="min-h-screen w-full flex bg-gradient-to-br from-indigo-50 to-blue-100">
      {/* Left Sidebar - 1/5 width */}
      <div className="hidden md:block w-1/5 p-4">
        <div className="h-full rounded-lg bg-white bg-opacity-30 backdrop-blur-sm shadow-sm"></div>
      </div>
      
      {/* Center Content - 3/5 width (twice as wide as each sidebar) */}
      <div className="w-full md:w-3/5 flex items-center justify-center">
        <div className={`w-full ${maxWidth} mx-auto p-6 sm:p-8 bg-white rounded-2xl shadow-xl border border-blue-100 ${className}`}>
          {children}
        </div>
      </div>
      
      {/* Right Sidebar - 1/5 width */}
      <div className="hidden md:block w-1/5 p-4">
        <div className="h-full rounded-lg bg-white bg-opacity-30 backdrop-blur-sm shadow-sm"></div>
      </div>
    </div>
  );
};

// Content container for dashboard-like pages
interface ContentContainerProps {
  children: ReactNode;
  maxWidth?: string;
  className?: string;
}

export const ContentContainer: React.FC<ContentContainerProps> = ({ 
  children, 
  maxWidth = "max-w-4xl",
  className = ""
}) => {
  return (
    <div className={`mx-auto ${maxWidth} px-4 sm:px-6 w-full ${className}`}>
      {children}
    </div>
  );
};

// Section header with icon
interface SectionHeaderProps {
  title: string;
  icon?: React.ReactNode;
  className?: string;
  centerOnMobile?: boolean;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({ 
  title, 
  icon, 
  className = "",
  centerOnMobile = true
}) => {
  return (
    <div className={`mb-6 flex items-center ${centerOnMobile ? 'justify-center md:justify-start' : 'justify-start'} ${className}`}>
      {icon && <div className="mr-3">{icon}</div>}
      <h1 className="text-2xl md:text-3xl font-bold text-gray-800">{title}</h1>
    </div>
  );
};
