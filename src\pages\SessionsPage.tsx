import React, { useState } from 'react';
import { SessionList } from '../components/SessionList';
import { SessionForm } from '../components/SessionForm';
import { AttendanceTracker } from '../components/AttendanceTracker';
import type { Session } from '../types';

type ViewMode = 'list' | 'create' | 'edit' | 'detail' | 'attendance';

export const SessionsPage: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedSession, setSelectedSession] = useState<Session | null>(null);

  const handleCreateSession = () => {
    setSelectedSession(null);
    setViewMode('create');
  };

  const handleEditSession = (session: Session) => {
    setSelectedSession(session);
    setViewMode('edit');
  };

  const handleSelectSession = (session: Session) => {
    setSelectedSession(session);
    setViewMode('detail');
  };

  const handleTrackAttendance = (session: Session) => {
    setSelectedSession(session);
    setViewMode('attendance');
  };

  const handleSaveSession = (session: Session) => {
    setSelectedSession(session);
    setViewMode('list');
  };

  const handleCancel = () => {
    setSelectedSession(null);
    setViewMode('list');
  };

  const renderContent = () => {
    switch (viewMode) {
      case 'create':
        return (
          <SessionForm
            onSave={handleSaveSession}
            onCancel={handleCancel}
          />
        );
      
      case 'edit':
        return selectedSession ? (
          <SessionForm
            session={selectedSession}
            onSave={handleSaveSession}
            onCancel={handleCancel}
          />
        ) : null;
      
      case 'detail':
        // TODO: Create SessionDetail component
        return (
          <div className="text-center py-8">
            <p className="text-gray-500">Session detail view coming soon</p>
            <button
              onClick={() => setViewMode('list')}
              className="mt-4 text-indigo-600 hover:text-indigo-800"
            >
              Back to Sessions
            </button>
          </div>
        );

      case 'attendance':
        return selectedSession ? (
          <AttendanceTracker
            session={selectedSession}
            onClose={() => setViewMode('list')}
          />
        ) : null;
      
      case 'list':
      default:
        return (
          <SessionList
            onSelectSession={handleSelectSession}
            onEditSession={handleEditSession}
            onCreateSession={handleCreateSession}
            onTrackAttendance={handleTrackAttendance}
            showActions={true}
          />
        );
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {renderContent()}
    </div>
  );
};
