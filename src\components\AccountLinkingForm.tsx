import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Search, UserPlus, Calendar, Shield } from 'lucide-react';
import { accountLinkService } from '../services';
import type { Student } from '../types';
import { useAuth } from '../contexts/AuthContext';

interface AccountLinkingFormProps {
  onSuccess: () => void;
}

interface SearchForm {
  searchTerm: string;
}

interface LinkRequestForm {
  relationshipType: 'self' | 'guardian';
  requestMessage: string;
}

export const AccountLinkingForm: React.FC<AccountLinkingFormProps> = ({ onSuccess }) => {
  const { state } = useAuth();
  const { profile } = state;
  const [searchResults, setSearchResults] = useState<Array<{
    id: string;
    first_name: string;
    last_name: string;
    belt_color: string;
    date_of_birth: string;
    hasUserAccount: boolean;
    hasGuardian: boolean;
  }>>([]);
  const [selectedStudent, setSelectedStudent] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const searchForm = useForm<SearchForm>();
  const linkForm = useForm<LinkRequestForm>({
    defaultValues: {
      relationshipType: 'guardian',
      requestMessage: ''
    }
  });

  const handleSearch = async (data: SearchForm) => {
    if (!data.searchTerm.trim()) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await accountLinkService.searchStudentsForLinking(data.searchTerm);
      if (result.error) {
        setError(result.error);
      } else {
        setSearchResults(result.data);
      }
    } catch (err) {
      setError('Failed to search students');
    } finally {
      setLoading(false);
    }
  };

  const handleLinkRequest = async (data: LinkRequestForm) => {
    if (!selectedStudent || !profile) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await accountLinkService.createRequest(
        selectedStudent.id,
        profile.id,
        data.relationshipType,
        data.requestMessage
      );
      
      if (result.error) {
        setError(result.error);
      } else {
        setSuccess('Account link request submitted successfully! An instructor will review your request.');
        setSelectedStudent(null);
        setSearchResults([]);
        searchForm.reset();
        linkForm.reset();
        setTimeout(() => {
          onSuccess();
        }, 2000);
      }
    } catch (err) {
      setError('Failed to submit link request');
    } finally {
      setLoading(false);
    }
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return age - 1;
    }
    return age;
  };

  const getBeltColorBadge = (beltColor: string) => {
    const colorMap: Record<string, string> = {
      white: 'bg-gray-100 text-gray-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      orange: 'bg-orange-100 text-orange-800',
      green: 'bg-green-100 text-green-800',
      blue: 'bg-blue-100 text-blue-800',
      purple: 'bg-purple-100 text-purple-800',
      brown: 'bg-amber-100 text-amber-800',
      black: 'bg-gray-800 text-white'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorMap[beltColor] || 'bg-gray-100 text-gray-800'}`}>
        {beltColor.charAt(0).toUpperCase() + beltColor.slice(1)} Belt
      </span>
    );
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center mb-6">
          <UserPlus className="h-6 w-6 text-indigo-600 mr-3" />
          <h2 className="text-xl font-semibold text-gray-900">Link to Student Account</h2>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div className="text-sm text-red-600">{error}</div>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
            <div className="text-sm text-green-600">{success}</div>
          </div>
        )}

        {/* Search Form */}
        <form onSubmit={searchForm.handleSubmit(handleSearch)} className="mb-6">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search by student name..."
                {...searchForm.register('searchTerm', { required: true })}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {loading ? 'Searching...' : 'Search'}
            </button>
          </div>
        </form>

        {/* Search Results */}
        {searchResults.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Search Results</h3>
            <div className="space-y-3">
              {searchResults.map((student) => (
                <div
                  key={student.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedStudent?.id === student.id
                      ? 'border-indigo-500 bg-indigo-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedStudent(student)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                          <span className="text-sm font-medium text-indigo-600">
                            {student.first_name[0]}{student.last_name[0]}
                          </span>
                        </div>
                      </div>
                      <div>
                        <div className="flex items-center space-x-3">
                          <p className="text-sm font-medium text-gray-900">
                            {student.first_name} {student.last_name}
                          </p>
                          {getBeltColorBadge(student.belt_color)}
                        </div>
                        <div className="flex items-center space-x-4 mt-1">
                          <p className="text-sm text-gray-500">
                            Age: {calculateAge(student.date_of_birth)}
                          </p>
                          <div className="flex items-center space-x-2">
                            {student.hasUserAccount && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Has Account
                              </span>
                            )}
                            {student.hasGuardian && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Has Guardian
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Link Request Form */}
        {selectedStudent && (
          <form onSubmit={linkForm.handleSubmit(handleLinkRequest)} className="space-y-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-md font-medium text-gray-900 mb-2">
                Request to link with: {selectedStudent.first_name} {selectedStudent.last_name}
              </h4>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Relationship Type
              </label>
              <div className="space-y-2">
                <div className="flex items-center">
                  <input
                    type="radio"
                    value="self"
                    {...linkForm.register('relationshipType')}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                  />
                  <label className="ml-2 block text-sm text-gray-900">
                    This is my own account (I am the student)
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="radio"
                    value="guardian"
                    {...linkForm.register('relationshipType')}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                  />
                  <label className="ml-2 block text-sm text-gray-900">
                    I am a parent/guardian of this student
                  </label>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Message to Instructor (Optional)
              </label>
              <textarea
                {...linkForm.register('requestMessage')}
                rows={3}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Please provide any additional information to help verify your relationship to this student..."
              />
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => setSelectedStudent(null)}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {loading ? 'Submitting...' : 'Submit Request'}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};
