import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { ProfileForm } from '../components/ProfileForm';
import { User, Settings } from 'lucide-react';
import { ContentContainer, SectionHeader, PageCenter } from '../components/PageLayout';

export const ProfilePage: React.FC = () => {
  const { state, dispatch } = useAuth();
  const { user, profile, loading } = state;
  const navigate = useNavigate();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-pulse flex space-x-4">
          <div className="rounded-full bg-gray-200 h-10 w-10"></div>
          <div className="flex-1 space-y-4 py-1">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  if (!user) {
    return (
      <PageCenter>
        <div className="text-center p-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Please sign in</h2>
          <p className="text-gray-600">You need to be signed in to view your profile.</p>
          <Link
            to="/signin"
            className="mt-4 inline-block px-6 py-2.5 bg-indigo-600 text-white font-medium rounded-lg shadow-md hover:bg-indigo-700"
          >
            Sign In
          </Link>
        </div>
      </PageCenter>
    );
  }
  
  return (
    <div className="min-h-screen w-full flex bg-gradient-to-br from-indigo-50 to-blue-100">
      {/* Left Sidebar - 1/5 width */}
      <div className="hidden md:block w-1/5 p-4">
        <div className="h-full rounded-lg bg-white bg-opacity-30 backdrop-blur-sm shadow-sm"></div>
      </div>
      
      {/* Center Content - 3/5 width */}
      <div className="w-full md:w-3/5 py-8">
        <ContentContainer maxWidth="w-full">
          <SectionHeader 
            title="Your Profile" 
            icon={<User className="w-8 h-8 text-indigo-600" />}
          />
          
          <div className="bg-white rounded-xl shadow-md p-6 mb-8">
            <div className="flex items-center justify-center md:justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-800 flex items-center">
                <Settings className="w-5 h-5 mr-2 text-indigo-500" />
                {profile ? 'Edit Profile Information' : 'Complete Your Profile'}
              </h2>
            </div>
            
            {!profile && (
              <div className="p-4 bg-yellow-50 border-l-4 border-yellow-400 text-yellow-700 mb-6">
                <p>
                  Your profile information is incomplete. Please fill out the form below to continue.
                </p>
              </div>
            )}
            
            <ProfileForm onSaved={() => {
              // Force a refresh of the profile data after saving
              if (user) {
                // Get fresh profile data from API
                import('../services/authService').then(({ fetchUserProfile }) => {
                  fetchUserProfile(user.id).then(({ data }) => {
                    if (data) {
                      // Update the context with the new profile data
                      dispatch({ type: 'SET_PROFILE', payload: data });
                      
                      // Redirect to the dashboard after a short delay
                      setTimeout(() => {
                        navigate('/dashboard');
                      }, 500); // Half-second delay to show success message
                    }
                  });
                });
              }
            }} />
          </div>
        </ContentContainer>
      </div>
      
      {/* Right Sidebar - 1/5 width */}
      <div className="hidden md:block w-1/5 p-4">
        <div className="h-full rounded-lg bg-white bg-opacity-30 backdrop-blur-sm shadow-sm"></div>
      </div>
    </div>
  );
};
