-- Enhanced Exercise Tracking Schema
-- Drop existing tables if they exist
DROP TABLE IF EXISTS fitness_records CASCADE;
DROP TABLE IF EXISTS exercises CASCADE;

-- Create custom types/enums for exercises
CREATE TYPE exercise_type AS ENUM ('cardio', 'strength', 'flexibility', 'sports', 'martial_arts');
CREATE TYPE measurement_type AS ENUM ('distance', 'time', 'sets', 'reps', 'weight', 'calories', 'speed', 'heart_rate');
CREATE TYPE verification_status AS ENUM ('unverified', 'self_verified', 'instructor_verified');

-- Exercise definitions table
CREATE TABLE exercises (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    exercise_type exercise_type NOT NULL,
    instructions TEXT,
    muscle_groups TEXT[], -- Array of muscle groups
    equipment_needed TEXT[],
    difficulty_level INTEGER CHECK (difficulty_level >= 1 AND difficulty_level <= 5),
    created_by UUID REFERENCES profiles(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Exercise measurements configuration
CREATE TABLE exercise_measurements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    exercise_id UUID REFERENCES exercises(id) ON DELETE CASCADE,
    measurement_type measurement_type NOT NULL,
    unit TEXT NOT NULL, -- e.g., 'km', 'minutes', 'kg', 'reps'
    is_required BOOLEAN DEFAULT false,
    is_primary BOOLEAN DEFAULT false, -- Primary measurement for leaderboards
    display_order INTEGER DEFAULT 0,
    UNIQUE(exercise_id, measurement_type)
);

-- Student exercise logs
CREATE TABLE exercise_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    student_id UUID REFERENCES students(id) NOT NULL,
    exercise_id UUID REFERENCES exercises(id) NOT NULL,
    verification_status verification_status DEFAULT 'unverified',
    verified_by UUID REFERENCES profiles(id), -- instructor who verified
    verified_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    proof_url TEXT, -- URL to image/video proof
    session_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Exercise log measurements (the actual performance data)
CREATE TABLE exercise_log_measurements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    exercise_log_id UUID REFERENCES exercise_logs(id) ON DELETE CASCADE,
    measurement_type measurement_type NOT NULL,
    value DECIMAL(10,2) NOT NULL,
    unit TEXT NOT NULL,
    UNIQUE(exercise_log_id, measurement_type)
);

-- Personal records table
CREATE TABLE personal_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    student_id UUID REFERENCES students(id) NOT NULL,
    exercise_id UUID REFERENCES exercises(id) NOT NULL,
    measurement_type measurement_type NOT NULL,
    best_value DECIMAL(10,2) NOT NULL,
    unit TEXT NOT NULL,
    achieved_at TIMESTAMP WITH TIME ZONE NOT NULL,
    exercise_log_id UUID REFERENCES exercise_logs(id),
    UNIQUE(student_id, exercise_id, measurement_type)
);

-- Leaderboard rankings
CREATE TABLE leaderboards (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    exercise_id UUID REFERENCES exercises(id) NOT NULL,
    measurement_type measurement_type NOT NULL,
    student_id UUID REFERENCES students(id) NOT NULL,
    best_value DECIMAL(10,2) NOT NULL,
    unit TEXT NOT NULL,
    rank_position INTEGER,
    percentile DECIMAL(5,2),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(exercise_id, measurement_type, student_id)
);

-- Progress tracking goals
CREATE TABLE student_goals (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    student_id UUID REFERENCES students(id) NOT NULL,
    exercise_id UUID REFERENCES exercises(id) NOT NULL,
    measurement_type measurement_type NOT NULL,
    target_value DECIMAL(10,2) NOT NULL,
    current_value DECIMAL(10,2) DEFAULT 0,
    unit TEXT NOT NULL,
    target_date DATE,
    is_achieved BOOLEAN DEFAULT false,
    achieved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_exercise_logs_student_date ON exercise_logs(student_id, session_date DESC);
CREATE INDEX idx_exercise_logs_exercise ON exercise_logs(exercise_id);
CREATE INDEX idx_exercise_logs_verification ON exercise_logs(verification_status);
CREATE INDEX idx_personal_records_student ON personal_records(student_id);
CREATE INDEX idx_leaderboards_exercise_measurement ON leaderboards(exercise_id, measurement_type, rank_position);
CREATE INDEX idx_exercises_type ON exercises(exercise_type);

-- Sample exercises for martial arts
INSERT INTO exercises (name, exercise_type, description, muscle_groups, equipment_needed, difficulty_level) VALUES
('Push-ups', 'strength', 'Standard push-up exercise for upper body strength', ARRAY['chest', 'triceps', 'shoulders', 'core'], ARRAY[]::TEXT[], 2),
('Sit-ups', 'strength', 'Abdominal strengthening exercise', ARRAY['core', 'abs'], ARRAY[]::TEXT[], 1),
('Squats', 'strength', 'Lower body strengthening exercise', ARRAY['legs', 'glutes', 'core'], ARRAY[]::TEXT[], 2),
('Plank', 'strength', 'Core stability exercise', ARRAY['core', 'shoulders'], ARRAY[]::TEXT[], 2),
('Burpees', 'cardio', 'Full body cardio and strength exercise', ARRAY['full_body'], ARRAY[]::TEXT[], 4),
('Running', 'cardio', 'Cardiovascular endurance exercise', ARRAY['legs', 'cardiovascular'], ARRAY['running_shoes'], 2),
('Jumping Jacks', 'cardio', 'Basic cardio warm-up exercise', ARRAY['full_body', 'cardiovascular'], ARRAY[]::TEXT[], 1),
('Mountain Climbers', 'cardio', 'High intensity cardio exercise', ARRAY['core', 'shoulders', 'legs'], ARRAY[]::TEXT[], 3),
('Kicks (High)', 'martial_arts', 'High kick technique practice', ARRAY['legs', 'core', 'flexibility'], ARRAY[]::TEXT[], 3),
('Punches (Speed)', 'martial_arts', 'Speed punching technique', ARRAY['arms', 'shoulders', 'core'], ARRAY['punching_bag'], 2),
('Forms/Kata', 'martial_arts', 'Traditional martial arts forms', ARRAY['full_body', 'coordination'], ARRAY[]::TEXT[], 3),
('Flexibility/Stretching', 'flexibility', 'Flexibility and mobility work', ARRAY['full_body'], ARRAY[]::TEXT[], 1);

-- Sample exercise measurements
INSERT INTO exercise_measurements (exercise_id, measurement_type, unit, is_required, is_primary, display_order) 
SELECT 
    e.id,
    'reps'::measurement_type,
    'reps',
    true,
    true,
    1
FROM exercises e WHERE e.name IN ('Push-ups', 'Sit-ups', 'Squats');

INSERT INTO exercise_measurements (exercise_id, measurement_type, unit, is_required, is_primary, display_order) 
SELECT 
    e.id,
    'time'::measurement_type,
    'seconds',
    true,
    true,
    1
FROM exercises e WHERE e.name IN ('Plank', 'Running');

INSERT INTO exercise_measurements (exercise_id, measurement_type, unit, is_required, is_primary, display_order) 
SELECT 
    e.id,
    'reps'::measurement_type,
    'reps',
    true,
    true,
    1
FROM exercises e WHERE e.name IN ('Burpees', 'Jumping Jacks', 'Mountain Climbers');

-- Function to update personal records
CREATE OR REPLACE FUNCTION update_personal_record()
RETURNS TRIGGER AS $$
BEGIN
    -- Only process verified entries
    IF NEW.verification_status IN ('self_verified', 'instructor_verified') THEN
        -- Update personal records for each measurement
        INSERT INTO personal_records (student_id, exercise_id, measurement_type, best_value, unit, achieved_at, exercise_log_id)
        SELECT 
            NEW.student_id,
            NEW.exercise_id,
            elm.measurement_type,
            elm.value,
            elm.unit,
            NEW.session_date,
            NEW.id
        FROM exercise_log_measurements elm
        WHERE elm.exercise_log_id = NEW.id
        ON CONFLICT (student_id, exercise_id, measurement_type)
        DO UPDATE SET
            best_value = CASE 
                WHEN personal_records.measurement_type IN ('distance', 'reps', 'weight') 
                THEN GREATEST(personal_records.best_value, EXCLUDED.best_value)
                WHEN personal_records.measurement_type = 'time'
                THEN LEAST(personal_records.best_value, EXCLUDED.best_value)
                ELSE EXCLUDED.best_value
            END,
            achieved_at = CASE
                WHEN (personal_records.measurement_type IN ('distance', 'reps', 'weight') AND EXCLUDED.best_value > personal_records.best_value)
                  OR (personal_records.measurement_type = 'time' AND EXCLUDED.best_value < personal_records.best_value)
                THEN EXCLUDED.achieved_at
                ELSE personal_records.achieved_at
            END,
            exercise_log_id = CASE
                WHEN (personal_records.measurement_type IN ('distance', 'reps', 'weight') AND EXCLUDED.best_value > personal_records.best_value)
                  OR (personal_records.measurement_type = 'time' AND EXCLUDED.best_value < personal_records.best_value)
                THEN EXCLUDED.exercise_log_id
                ELSE personal_records.exercise_log_id
            END;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update personal records
CREATE TRIGGER trigger_update_personal_record
    AFTER INSERT OR UPDATE ON exercise_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_personal_record();
