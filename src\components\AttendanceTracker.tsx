import React, { useState, useEffect } from 'react';
import { Check, X, Clock, Users, Save, Search } from 'lucide-react';
import { attendanceService, studentService } from '../services';
import type { Session, Student, Attendance } from '../types';
import { useAuth } from '../contexts/AuthContext';

interface AttendanceTrackerProps {
  session: Session;
  onClose: () => void;
}

interface StudentAttendance {
  student: Student;
  attendance?: Attendance;
  status: 'attended_paid' | 'attended_unpaid' | 'absent' | 'not_recorded';
}

export const AttendanceTracker: React.FC<AttendanceTrackerProps> = ({
  session,
  onClose
}) => {
  const { state } = useAuth();
  const { profile } = state;
  const [students, setStudents] = useState<StudentAttendance[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  useEffect(() => {
    loadStudentsAndAttendance();
  }, [session.id]);

  const loadStudentsAndAttendance = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load all students
      const studentsResult = await studentService.getStudents({ active: true });
      if (studentsResult.error) {
        setError(studentsResult.error);
        return;
      }

      // Load existing attendance for this session
      const attendanceResult = await attendanceService.getSessionAttendance(session.id);
      if (attendanceResult.error) {
        setError(attendanceResult.error);
        return;
      }

      // Combine students with their attendance status
      const studentAttendance: StudentAttendance[] = studentsResult.data.map(student => {
        const existingAttendance = attendanceResult.data.find(
          att => att.student_id === student.id
        );
        
        return {
          student,
          attendance: existingAttendance,
          status: existingAttendance?.status || 'not_recorded'
        };
      });

      setStudents(studentAttendance);
    } catch (err) {
      setError('Failed to load students and attendance');
    } finally {
      setLoading(false);
    }
  };

  const updateAttendanceStatus = (studentId: string, status: 'attended_paid' | 'attended_unpaid' | 'absent') => {
    setStudents(prev => prev.map(item => 
      item.student.id === studentId 
        ? { ...item, status }
        : item
    ));
  };

  const saveAttendance = async () => {
    if (!profile) return;
    
    setSaving(true);
    setError(null);

    try {
      const attendanceRecords = students
        .filter(item => item.status !== 'not_recorded')
        .map(item => ({
          session_id: session.id,
          student_id: item.student.id,
          status: item.status as 'attended_paid' | 'attended_unpaid' | 'absent'
        }));

      const result = await attendanceService.recordBulkAttendance(
        attendanceRecords,
        profile.id
      );

      if (result.error) {
        setError(result.error);
      } else {
        // Reload to get updated data
        await loadStudentsAndAttendance();
        // Show success message briefly
        setError(null);
      }
    } catch (err) {
      setError('Failed to save attendance');
    } finally {
      setSaving(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'attended_paid':
        return <Check className="h-5 w-5 text-green-500" />;
      case 'attended_unpaid':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'absent':
        return <X className="h-5 w-5 text-red-500" />;
      default:
        return <div className="h-5 w-5 border-2 border-gray-300 rounded"></div>;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'attended_paid':
        return 'bg-green-50 border-green-200';
      case 'attended_unpaid':
        return 'bg-yellow-50 border-yellow-200';
      case 'absent':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const filteredStudents = students.filter(item => {
    const matchesSearch = searchTerm === '' || 
      `${item.student.first_name} ${item.student.last_name}`.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterStatus === 'all' || item.status === filterStatus;
    
    return matchesSearch && matchesFilter;
  });

  const getAttendanceStats = () => {
    const total = students.length;
    const attended = students.filter(s => s.status === 'attended_paid' || s.status === 'attended_unpaid').length;
    const paid = students.filter(s => s.status === 'attended_paid').length;
    const unpaid = students.filter(s => s.status === 'attended_unpaid').length;
    const absent = students.filter(s => s.status === 'absent').length;
    const notRecorded = students.filter(s => s.status === 'not_recorded').length;

    return { total, attended, paid, unpaid, absent, notRecorded };
  };

  const stats = getAttendanceStats();

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 bg-indigo-600 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold">Attendance Tracking</h2>
            <p className="text-indigo-200 mt-1">
              {session.name} - {new Date(session.date).toLocaleDateString()} at {session.start_time}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-indigo-200 hover:text-white"
          >
            <X className="h-6 w-6" />
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="px-6 py-4 bg-gray-50 border-b">
        <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
            <div className="text-sm text-gray-600">Total Students</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.attended}</div>
            <div className="text-sm text-gray-600">Attended</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-700">{stats.paid}</div>
            <div className="text-sm text-gray-600">Paid</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{stats.unpaid}</div>
            <div className="text-sm text-gray-600">Unpaid</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{stats.absent}</div>
            <div className="text-sm text-gray-600">Absent</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-600">{stats.notRecorded}</div>
            <div className="text-sm text-gray-600">Not Recorded</div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="px-6 py-4 border-b">
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search students..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="all">All Students</option>
              <option value="attended_paid">Attended (Paid)</option>
              <option value="attended_unpaid">Attended (Unpaid)</option>
              <option value="absent">Absent</option>
              <option value="not_recorded">Not Recorded</option>
            </select>
          </div>

          <button
            onClick={saveAttendance}
            disabled={saving}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {saving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Attendance
          </button>
        </div>
      </div>

      {error && (
        <div className="px-6 py-4 bg-red-50 border-b border-red-200">
          <div className="text-sm text-red-600">{error}</div>
        </div>
      )}

      {/* Student List */}
      <div className="max-h-96 overflow-y-auto">
        {filteredStudents.length === 0 ? (
          <div className="text-center py-8">
            <Users className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No students found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try adjusting your search or filter criteria.
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredStudents.map((item) => (
              <div
                key={item.student.id}
                className={`px-6 py-4 ${getStatusColor(item.status)}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                        <span className="text-sm font-medium text-indigo-600">
                          {item.student.first_name[0]}{item.student.last_name[0]}
                        </span>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {item.student.first_name} {item.student.last_name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {item.student.belt_color.charAt(0).toUpperCase() + item.student.belt_color.slice(1)} Belt
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(item.status)}
                    <div className="flex space-x-1">
                      <button
                        onClick={() => updateAttendanceStatus(item.student.id, 'attended_paid')}
                        className={`px-3 py-1 text-xs font-medium rounded ${
                          item.status === 'attended_paid'
                            ? 'bg-green-600 text-white'
                            : 'bg-green-100 text-green-800 hover:bg-green-200'
                        }`}
                      >
                        Paid
                      </button>
                      <button
                        onClick={() => updateAttendanceStatus(item.student.id, 'attended_unpaid')}
                        className={`px-3 py-1 text-xs font-medium rounded ${
                          item.status === 'attended_unpaid'
                            ? 'bg-yellow-600 text-white'
                            : 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                        }`}
                      >
                        Unpaid
                      </button>
                      <button
                        onClick={() => updateAttendanceStatus(item.student.id, 'absent')}
                        className={`px-3 py-1 text-xs font-medium rounded ${
                          item.status === 'absent'
                            ? 'bg-red-600 text-white'
                            : 'bg-red-100 text-red-800 hover:bg-red-200'
                        }`}
                      >
                        Absent
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
