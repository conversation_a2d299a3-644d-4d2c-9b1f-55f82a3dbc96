-- Add Row Level Security policies for all tables

-- Profiles policies (already exist, but ensuring they're correct)
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Instructors and admins can view all profiles" ON profiles;

CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Instructors and admins can view all profiles" ON profiles
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('instructor', 'admin')
    )
  );

-- Students policies
DROP POLICY IF EXISTS "Instructors and admins can manage students" ON students;
DROP POLICY IF EXISTS "Users can view linked students" ON students;
DROP POLICY IF EXISTS "Guardians can view their students" ON students;

CREATE POLICY "Instructors and admins can manage students" ON students
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('instructor', 'admin')
    )
  );

CREATE POLICY "Users can view linked students" ON students
  FOR SELECT USING (
    user_id = auth.uid() OR 
    guardian_id = auth.uid()
  );

CREATE POLICY "Guardians can view their students" ON students
  FOR SELECT USING (guardian_id = auth.uid());

-- Sessions policies
DROP POLICY IF EXISTS "Instructors and admins can manage sessions" ON sessions;
DROP POLICY IF EXISTS "All authenticated users can view sessions" ON sessions;

CREATE POLICY "Instructors and admins can manage sessions" ON sessions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('instructor', 'admin')
    )
  );

CREATE POLICY "All authenticated users can view sessions" ON sessions
  FOR SELECT USING (auth.role() = 'authenticated');

-- Attendance policies
DROP POLICY IF EXISTS "Instructors and admins can manage attendance" ON attendance;
DROP POLICY IF EXISTS "Users can view attendance for linked students" ON attendance;

CREATE POLICY "Instructors and admins can manage attendance" ON attendance
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('instructor', 'admin')
    )
  );

CREATE POLICY "Users can view attendance for linked students" ON attendance
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM students 
      WHERE id = attendance.student_id 
      AND (user_id = auth.uid() OR guardian_id = auth.uid())
    )
  );

-- Syllabus policies (read-only for most users)
DROP POLICY IF EXISTS "All authenticated users can view syllabus" ON syllabus;
DROP POLICY IF EXISTS "Admins can manage syllabus" ON syllabus;

CREATE POLICY "All authenticated users can view syllabus" ON syllabus
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admins can manage syllabus" ON syllabus
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Gradings policies
DROP POLICY IF EXISTS "Instructors and admins can manage gradings" ON gradings;
DROP POLICY IF EXISTS "Users can view gradings for linked students" ON gradings;

CREATE POLICY "Instructors and admins can manage gradings" ON gradings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('instructor', 'admin')
    )
  );

CREATE POLICY "Users can view gradings for linked students" ON gradings
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM students 
      WHERE id = gradings.student_id 
      AND (user_id = auth.uid() OR guardian_id = auth.uid())
    )
  );

-- Exercises policies
DROP POLICY IF EXISTS "Instructors and admins can manage exercises" ON exercises;
DROP POLICY IF EXISTS "All authenticated users can view exercises" ON exercises;

CREATE POLICY "Instructors and admins can manage exercises" ON exercises
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('instructor', 'admin')
    )
  );

CREATE POLICY "All authenticated users can view exercises" ON exercises
  FOR SELECT USING (auth.role() = 'authenticated');

-- Fitness records policies
DROP POLICY IF EXISTS "Instructors and admins can manage fitness records" ON fitness_records;
DROP POLICY IF EXISTS "Users can view fitness records for linked students" ON fitness_records;

CREATE POLICY "Instructors and admins can manage fitness records" ON fitness_records
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('instructor', 'admin')
    )
  );

CREATE POLICY "Users can view fitness records for linked students" ON fitness_records
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM students 
      WHERE id = fitness_records.student_id 
      AND (user_id = auth.uid() OR guardian_id = auth.uid())
    )
  );

-- Account link requests policies
DROP POLICY IF EXISTS "Users can manage their own requests" ON account_link_requests;
DROP POLICY IF EXISTS "Instructors and admins can manage all requests" ON account_link_requests;

CREATE POLICY "Users can manage their own requests" ON account_link_requests
  FOR ALL USING (requested_by = auth.uid());

CREATE POLICY "Instructors and admins can manage all requests" ON account_link_requests
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('instructor', 'admin')
    )
  );
