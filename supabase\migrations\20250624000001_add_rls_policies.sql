-- Add Row Level Security policies for all tables

-- Profiles policies (fixed to avoid infinite recursion)
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;

-- Users can always view and update their own profile
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Users can insert their own profile (for profile creation)
CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- First, let's create a function to check user roles safely
CREATE OR REPLACE FUNCTION auth.user_role()
RETURNS TEXT AS $$
  SELECT role FROM profiles WHERE id = auth.uid()
$$ LANGUAGE SQL SECURITY DEFINER;

-- Students policies
DROP POLICY IF EXISTS "Instructors and admins can manage students" ON students;
DROP POLICY IF EXISTS "Users can view linked students" ON students;

CREATE POLICY "Instructors and admins can manage students" ON students
  FOR ALL USING (auth.user_role() IN ('instructor', 'admin'));

CREATE POLICY "Users can view linked students" ON students
  FOR SELECT USING (
    user_id = auth.uid() OR
    guardian_id = auth.uid() OR
    auth.user_role() IN ('instructor', 'admin')
  );

-- Sessions policies
DROP POLICY IF EXISTS "Instructors and admins can manage sessions" ON sessions;
DROP POLICY IF EXISTS "All authenticated users can view sessions" ON sessions;

CREATE POLICY "Instructors and admins can manage sessions" ON sessions
  FOR ALL USING (auth.user_role() IN ('instructor', 'admin'));

CREATE POLICY "All authenticated users can view sessions" ON sessions
  FOR SELECT USING (auth.role() = 'authenticated');

-- Attendance policies
DROP POLICY IF EXISTS "Instructors and admins can manage attendance" ON attendance;
DROP POLICY IF EXISTS "Users can view attendance for linked students" ON attendance;

CREATE POLICY "Instructors and admins can manage attendance" ON attendance
  FOR ALL USING (auth.user_role() IN ('instructor', 'admin'));

CREATE POLICY "Users can view attendance for linked students" ON attendance
  FOR SELECT USING (
    auth.user_role() IN ('instructor', 'admin') OR
    EXISTS (
      SELECT 1 FROM students
      WHERE id = attendance.student_id
      AND (user_id = auth.uid() OR guardian_id = auth.uid())
    )
  );

-- Syllabus policies (read-only for most users)
DROP POLICY IF EXISTS "All authenticated users can view syllabus" ON syllabus;
DROP POLICY IF EXISTS "Admins can manage syllabus" ON syllabus;

CREATE POLICY "All authenticated users can view syllabus" ON syllabus
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admins can manage syllabus" ON syllabus
  FOR ALL USING (auth.user_role() = 'admin');

-- Gradings policies
DROP POLICY IF EXISTS "Instructors and admins can manage gradings" ON gradings;
DROP POLICY IF EXISTS "Users can view gradings for linked students" ON gradings;

CREATE POLICY "Instructors and admins can manage gradings" ON gradings
  FOR ALL USING (auth.user_role() IN ('instructor', 'admin'));

CREATE POLICY "Users can view gradings for linked students" ON gradings
  FOR SELECT USING (
    auth.user_role() IN ('instructor', 'admin') OR
    EXISTS (
      SELECT 1 FROM students
      WHERE id = gradings.student_id
      AND (user_id = auth.uid() OR guardian_id = auth.uid())
    )
  );

-- Exercises policies
DROP POLICY IF EXISTS "Instructors and admins can manage exercises" ON exercises;
DROP POLICY IF EXISTS "All authenticated users can view exercises" ON exercises;

CREATE POLICY "Instructors and admins can manage exercises" ON exercises
  FOR ALL USING (auth.user_role() IN ('instructor', 'admin'));

CREATE POLICY "All authenticated users can view exercises" ON exercises
  FOR SELECT USING (auth.role() = 'authenticated');

-- Fitness records policies
DROP POLICY IF EXISTS "Instructors and admins can manage fitness records" ON fitness_records;
DROP POLICY IF EXISTS "Users can view fitness records for linked students" ON fitness_records;

CREATE POLICY "Instructors and admins can manage fitness records" ON fitness_records
  FOR ALL USING (auth.user_role() IN ('instructor', 'admin'));

CREATE POLICY "Users can view fitness records for linked students" ON fitness_records
  FOR SELECT USING (
    auth.user_role() IN ('instructor', 'admin') OR
    EXISTS (
      SELECT 1 FROM students
      WHERE id = fitness_records.student_id
      AND (user_id = auth.uid() OR guardian_id = auth.uid())
    )
  );

-- Account link requests policies
DROP POLICY IF EXISTS "Users can manage their own requests" ON account_link_requests;
DROP POLICY IF EXISTS "Instructors and admins can manage all requests" ON account_link_requests;

CREATE POLICY "Users can manage their own requests" ON account_link_requests
  FOR ALL USING (requested_by = auth.uid());

CREATE POLICY "Instructors and admins can manage all requests" ON account_link_requests
  FOR ALL USING (auth.user_role() IN ('instructor', 'admin'));
