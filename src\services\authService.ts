import { supabase } from './supabaseClient';

export const signUp = async (email: string, password: string) => {
  // Use signUp with data retention option to ensure we can retry profile creation
  return await supabase.auth.signUp({ 
    email, 
    password,
    options: {
      // This ensures we can retry operations if profile creation fails
      data: {
        sign_up_timestamp: new Date().toISOString()
      }
    }
  });
};

export const signIn = async (email: string, password: string) => {
  return await supabase.auth.signInWithPassword({ email, password });
};

export const signOut = async () => {
  await supabase.auth.signOut();
};

export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  return user;
};

export const resetPassword = async (email: string) => {
  return await supabase.auth.resetPasswordForEmail(email);
};

export const updatePassword = async (newPassword: string) => {
  return await supabase.auth.updateUser({ password: newPassword });
};

export const onAuthStateChange = (callback: (event: string, session: any) => void) => {
  return supabase.auth.onAuthStateChange(callback);
};

// Email verification is handled by Supabase automatically on sign up.
export const resendVerificationEmail = async () => {
  // Get current user email
  const { data } = await supabase.auth.getUser();
  const email = data?.user?.email;
  
  if (!email) {
    return { error: new Error('No user is currently signed in') };
  }
  
  // Use the OTP sign-in method which will send a new email
  return await supabase.auth.signInWithOtp({
    email,
    options: {
      shouldCreateUser: false
    }
  });
};

export function getErrorMessage(error: any): string {
  return error?.message || 'An error occurred';
}

// New function to fetch user profile
export const fetchUserProfile = async (userId: string) => {
  // Use cache control option to ensure we get fresh data
  return await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single()
    .then((response) => {
      if (response.error && response.error.message.includes('No rows found')) {
        console.log('No profile found for user, may need to create one');
      }
      return response;
    });
};

// Create or update user profile
export const upsertProfile = async (
  userId: string,
  profileData: {
    first_name: string;
    last_name: string;
    role?: string;
    phone?: string;
    date_of_birth?: string;
    address?: any;
  }
) => {
  try {
    // First try direct upsert
    const { error } = await supabase.from('profiles').upsert(
      {
        id: userId,
        ...profileData,
      },
      { onConflict: 'id' }
    );
    
    // If direct upsert fails, try using the RPC function
    if (error) {
      console.error('Upsert profile error:', error);
      
      return await supabase.rpc('create_user_profile', {
        user_id: userId,
        user_first_name: profileData.first_name,
        user_last_name: profileData.last_name,
        user_role: profileData.role || 'student'
      });
    }
    
    return { error: null };
  } catch (error) {
    console.error('Profile update failed:', error);
    return { error };
  }
};

// Create initial profile with retry mechanism
export const createInitialProfile = async (
  userId: string,
  firstName: string,
  lastName: string,
  role: string = 'student'
) => {
  try {
    // First try direct insert
    const { error } = await supabase.from('profiles').insert({
      id: userId,
      first_name: firstName,
      last_name: lastName,
      role
    });
    
    // If direct insert fails, try using the RPC function
    if (error) {
      console.error('Create profile error:', error);
      
      return await supabase.rpc('create_user_profile', {
        user_id: userId,
        user_first_name: firstName,
        user_last_name: lastName,
        user_role: role
      });
    }
    
    return { error: null };
  } catch (error) {
    console.error('Profile creation failed:', error);
    return { error };
  }
};