import React, { useState, useEffect } from 'react';
import { Plus, Save, Trophy, Target, Calendar, User, CheckCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { supabase } from '../services/supabaseClient';
import { studentService } from '../services';
import { useAuth } from '../contexts/AuthContext';

interface Exercise {
  id: string;
  name: string;
  exercise_type: string;
  difficulty_level: number;
}

interface Student {
  id: string;
  first_name: string;
  last_name: string;
  belt_color: string;
}

interface ExerciseLog {
  id: string;
  student_id: string;
  exercise_id: string;
  session_date: string;
  verification_status: 'unverified' | 'self_verified' | 'instructor_verified';
  notes?: string;
  measurements: Array<{
    measurement_type: string;
    value: number;
    unit: string;
  }>;
  student: Student;
  exercise: Exercise;
}

interface RecordForm {
  student_id: string;
  exercise_id: string;
  session_date: string;
  notes: string;
  measurements: Record<string, string>;
}

const MEASUREMENT_TYPES = [
  { type: 'reps', label: 'Repetitions', unit: 'reps' },
  { type: 'time', label: 'Time', unit: 'seconds' },
  { type: 'distance', label: 'Distance', unit: 'meters' },
  { type: 'weight', label: 'Weight', unit: 'kg' },
  { type: 'sets', label: 'Sets', unit: 'sets' }
];

export const FitnessRecordTracker: React.FC = () => {
  const { state } = useAuth();
  const { profile } = state;
  const [exercises, setExercises] = useState<Exercise[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [recentLogs, setRecentLogs] = useState<ExerciseLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedExercise, setSelectedExercise] = useState<Exercise | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors }
  } = useForm<RecordForm>({
    defaultValues: {
      session_date: new Date().toISOString().split('T')[0]
    }
  });

  const watchedExerciseId = watch('exercise_id');

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    if (watchedExerciseId) {
      const exercise = exercises.find(e => e.id === watchedExerciseId);
      setSelectedExercise(exercise || null);
    }
  }, [watchedExerciseId, exercises]);

  const loadData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load exercises
      const { data: exercisesData, error: exercisesError } = await supabase
        .from('exercises')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (exercisesError) throw exercisesError;

      // Load students
      const studentsResult = await studentService.getStudents({ active: true });
      if (studentsResult.error) throw new Error(studentsResult.error);

      // Load recent exercise logs
      const { data: logsData, error: logsError } = await supabase
        .from('exercise_logs')
        .select(`
          *,
          student:student_id(id, first_name, last_name, belt_color),
          exercise:exercise_id(id, name, exercise_type, difficulty_level)
        `)
        .order('created_at', { ascending: false })
        .limit(10);

      if (logsError) throw logsError;

      setExercises(exercisesData || []);
      setStudents(studentsResult.data);
      setRecentLogs(logsData || []);
    } catch (err: any) {
      setError(err.message || 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: RecordForm) => {
    if (!profile || !selectedExercise) return;

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      // Create exercise log
      const { data: logData, error: logError } = await supabase
        .from('exercise_logs')
        .insert([{
          student_id: data.student_id,
          exercise_id: data.exercise_id,
          session_date: data.session_date,
          notes: data.notes || null,
          verification_status: profile.role === 'instructor' || profile.role === 'admin' 
            ? 'instructor_verified' 
            : 'self_verified'
        }])
        .select()
        .single();

      if (logError) throw logError;

      // Create measurements
      const measurements = Object.entries(data.measurements)
        .filter(([_, value]) => value && value.trim() !== '')
        .map(([type, value]) => {
          const measurementType = MEASUREMENT_TYPES.find(m => m.type === type);
          return {
            exercise_log_id: logData.id,
            measurement_type: type,
            value: parseFloat(value),
            unit: measurementType?.unit || ''
          };
        });

      if (measurements.length > 0) {
        const { error: measurementsError } = await supabase
          .from('exercise_log_measurements')
          .insert(measurements);

        if (measurementsError) throw measurementsError;
      }

      setSuccess('Exercise record saved successfully!');
      reset({
        student_id: '',
        exercise_id: '',
        session_date: new Date().toISOString().split('T')[0],
        notes: '',
        measurements: {}
      });
      setSelectedExercise(null);
      
      // Reload recent logs
      await loadData();
    } catch (err: any) {
      setError(err.message || 'Failed to save exercise record');
    } finally {
      setSaving(false);
    }
  };

  const getVerificationIcon = (status: string) => {
    switch (status) {
      case 'instructor_verified':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'self_verified':
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      default:
        return <Target className="h-4 w-4 text-gray-400" />;
    }
  };

  const getVerificationLabel = (status: string) => {
    switch (status) {
      case 'instructor_verified':
        return 'Instructor Verified';
      case 'self_verified':
        return 'Self Verified';
      default:
        return 'Unverified';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Record Form */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center mb-6">
          <Plus className="h-6 w-6 text-indigo-600 mr-3" />
          Record Exercise Performance
        </h2>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="text-sm text-red-600">{error}</div>
          </div>
        )}

        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="text-sm text-green-600">{success}</div>
          </div>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Student *
              </label>
              <select
                {...register('student_id', { required: 'Student is required' })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">Select Student</option>
                {students.map(student => (
                  <option key={student.id} value={student.id}>
                    {student.first_name} {student.last_name} ({student.belt_color} belt)
                  </option>
                ))}
              </select>
              {errors.student_id && (
                <p className="mt-1 text-sm text-red-600">{errors.student_id.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Exercise *
              </label>
              <select
                {...register('exercise_id', { required: 'Exercise is required' })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">Select Exercise</option>
                {exercises.map(exercise => (
                  <option key={exercise.id} value={exercise.id}>
                    {exercise.name} (Level {exercise.difficulty_level})
                  </option>
                ))}
              </select>
              {errors.exercise_id && (
                <p className="mt-1 text-sm text-red-600">{errors.exercise_id.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date *
              </label>
              <input
                type="date"
                {...register('session_date', { required: 'Date is required' })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              {errors.session_date && (
                <p className="mt-1 text-sm text-red-600">{errors.session_date.message}</p>
              )}
            </div>
          </div>

          {/* Measurements */}
          {selectedExercise && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Measurements</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {MEASUREMENT_TYPES.map(measurement => (
                  <div key={measurement.type}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {measurement.label} ({measurement.unit})
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      {...register(`measurements.${measurement.type}`)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      placeholder={`Enter ${measurement.label.toLowerCase()}`}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              {...register('notes')}
              rows={3}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Additional notes about the performance..."
            />
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={saving}
              className="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {saving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              {saving ? 'Saving...' : 'Save Record'}
            </button>
          </div>
        </form>
      </div>

      {/* Recent Records */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Trophy className="h-5 w-5 text-indigo-600 mr-2" />
            Recent Exercise Records
          </h3>
        </div>
        
        {recentLogs.length === 0 ? (
          <div className="p-12 text-center">
            <Target className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No exercise records</h3>
            <p className="mt-1 text-sm text-gray-500">
              Start recording student exercise performance to see them here.
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {recentLogs.map((log) => (
              <div key={log.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="text-sm font-medium text-gray-900">
                        {log.student.first_name} {log.student.last_name}
                      </span>
                      <span className="text-sm text-gray-500">•</span>
                      <span className="text-sm text-gray-900">{log.exercise.name}</span>
                      <span className="text-sm text-gray-500">•</span>
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-500">
                        {new Date(log.session_date).toLocaleDateString()}
                      </span>
                    </div>
                    
                    {log.measurements && log.measurements.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-2">
                        {log.measurements.map((measurement, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {measurement.value} {measurement.unit} {measurement.measurement_type}
                          </span>
                        ))}
                      </div>
                    )}
                    
                    {log.notes && (
                      <p className="text-sm text-gray-600 mb-2">{log.notes}</p>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    {getVerificationIcon(log.verification_status)}
                    <span className="text-xs text-gray-500">
                      {getVerificationLabel(log.verification_status)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
