{"name": "augmentmartialartscrm", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint src --ext .ts,.tsx", "preview": "vite preview", "format": "prettier --write src/**/*.{ts,tsx,js,jsx,json,css,md}"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@supabase/supabase-js": "^2.50.0", "date-fns": "^4.1.0", "lucide-react": "^0.516.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.0", "react-router-dom": "^6.30.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "prettier": "^3.5.3", "tailwindcss": "^3.3.3", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}