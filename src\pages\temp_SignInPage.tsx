import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { signIn, getErrorMessage } from '../services/authService';
import { useAuth } from '../contexts/AuthContext';
import { LogIn } from 'lucide-react';
import { useNavigate, Link } from 'react-router-dom';
import { PageCenter } from '../components/PageLayout';

const schema = z.object({
  email: z.string().email('Invalid email'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type SignInFormData = z.infer<typeof schema>;

export const SignInPage: React.FC = () => {
  const { dispatch } = useAuth();
  const [errorMsg, setErrorMsg] = useState('');
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
    reset,
  } = useForm<SignInFormData>({ resolver: zodResolver(schema) });

  const onSubmit = async (data: SignInFormData) => {
    setErrorMsg('');
    const { error, data: result } = await signIn(data.email, data.password);
    if (error) {
      setError('email', { message: getErrorMessage(error) });
      setErrorMsg(getErrorMessage(error));
      return;
    }
    if (result?.user) {
      dispatch({ type: 'LOGIN', payload: result.user });
      reset();
      navigate('/dashboard');
    }
  };
  return (
    <PageCenter>
        <div className="flex flex-col items-center mb-6">
          <LogIn className="w-10 h-10 text-indigo-600 mb-2" />
          <h2 className="text-3xl font-extrabold text-indigo-700">Sign In</h2>
        </div>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div>
            <label className="block mb-1 font-medium text-gray-700">Email</label>
            <input
              type="email"
              {...register('email')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              autoComplete="email"
            />
            {errors.email && <p className="text-red-600 text-sm mt-1">{errors.email.message}</p>}
          </div>
          <div>
            <label className="block mb-1 font-medium text-gray-700">Password</label>
            <input
              type="password"
              {...register('password')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              autoComplete="current-password"
            />
            {errors.password && <p className="text-red-600 text-sm mt-1">{errors.password.message}</p>}
          </div>
          <div className="flex justify-end">
            <Link to="/forgot-password" className="text-sm text-indigo-600 hover:text-indigo-800 font-medium">
              Forgot password?
            </Link>
          </div>
          <button
            type="submit"
            className="w-full bg-gradient-to-r from-indigo-600 to-blue-500 text-white py-2.5 rounded-lg font-bold text-lg shadow-md hover:from-indigo-700 hover:to-blue-600 transition"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Signing in...' : 'Sign In'}
          </button>
        </form>
        {errorMsg && <p className="text-red-600 text-center mt-4">{errorMsg}</p>}
        <div className="text-center mt-6">
          <p className="text-gray-600">
            Don't have an account?{' '}
            <Link to="/signup" className="text-indigo-600 hover:text-indigo-800 font-medium">
              Sign up
            </Link>
          </p>
        </div>
    </PageCenter>
  );
};
