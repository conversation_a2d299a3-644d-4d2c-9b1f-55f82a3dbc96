-- Drop existing policies and recreate them
DROP POLICY IF EXISTS "Users can read own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
DROP POLICY IF EXISTS "Service role can manage all profiles" ON profiles;

-- Enable Row Level Security (RLS)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Add RLS policy to allow authenticated users to read their own profile
CREATE POLICY "Users can read own profile"
  ON profiles FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

-- Add RLS policy to allow authenticated users to update their own profile
CREATE POLICY "Users can update own profile"
  ON profiles FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Add RLS policy to allow authenticated users to insert their own profile
CREATE POLICY "Users can insert own profile"
  ON profiles FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- Add RLS policy to allow the service role to manage all profiles
CREATE POLICY "Service role can manage all profiles"
  ON profiles
  TO service_role
  USING (true);

-- Create a stored procedure to handle user profile creation
CREATE OR REPLACE FUNCTION create_user_profile(
  user_id UUID,
  user_first_name TEXT,
  user_last_name TEXT,
  user_role TEXT DEFAULT 'student'
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  valid_role BOOLEAN;
BEGIN
  -- Check if role is valid
  SELECT (user_role IN ('admin', 'instructor', 'student', 'guardian')) INTO valid_role;
  
  IF NOT valid_role THEN
    RAISE EXCEPTION 'Invalid role: %', user_role;
  END IF;

  -- Insert profile
  INSERT INTO profiles (
    id,
    first_name,
    last_name,
    role
  ) VALUES (
    user_id,
    user_first_name,
    user_last_name,
    user_role
  )
  ON CONFLICT (id) DO UPDATE SET
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    role = EXCLUDED.role,
    updated_at = NOW();
    
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in create_user_profile: %', SQLERRM;
    RETURN FALSE;
END;
$$;

-- Drop the existing trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Add trigger to ensure profile is created for new users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, first_name, last_name, role)
  VALUES (new.id, '', '', 'student')
  ON CONFLICT (id) DO NOTHING;
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to create profile on user signup if it doesn't exist already
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
