import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Save, X, User, Calendar, Phone, MapPin, Shield, Heart } from 'lucide-react';
import { studentService } from '../services';
import type { Student, CreateStudentForm, Profile } from '../types';
import { useAuth } from '../contexts/AuthContext';

interface StudentFormProps {
  student?: Student;
  onSave: (student: Student) => void;
  onCancel: () => void;
}

export const StudentForm: React.FC<StudentFormProps> = ({
  student,
  onSave,
  onCancel
}) => {
  const { state } = useAuth();
  const { profile } = state;
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [guardians, setGuardians] = useState<Profile[]>([]);

  const beltColors = ['white', 'yellow', 'orange', 'green', 'blue', 'purple', 'brown', 'black'];

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<CreateStudentForm>({
    defaultValues: student ? {
      first_name: student.first_name,
      last_name: student.last_name,
      date_of_birth: student.date_of_birth,
      phone: student.phone || '',
      belt_color: student.belt_color,
      insurance_expiry: student.insurance_expiry || '',
      licence_number: student.licence_number || '',
      guardian_id: student.guardian_id || '',
      consent_social_media: student.consent_social_media,
      consent_assumption_risk: student.consent_assumption_risk,
      medical_conditions: student.medical_conditions || [],
      address: student.address || {},
      emergency_contact: student.emergency_contact || {}
    } : {
      first_name: '',
      last_name: '',
      date_of_birth: '',
      phone: '',
      belt_color: 'white',
      insurance_expiry: '',
      licence_number: '',
      guardian_id: '',
      consent_social_media: false,
      consent_assumption_risk: false,
      medical_conditions: [],
      address: {},
      emergency_contact: {}
    }
  });

  // Load guardians for selection
  useEffect(() => {
    const loadGuardians = async () => {
      // This would need a service to fetch users with guardian role
      // For now, we'll leave it empty
      setGuardians([]);
    };
    loadGuardians();
  }, []);

  const onSubmit = async (data: CreateStudentForm) => {
    setLoading(true);
    setError(null);

    try {
      // Clean up the data to handle empty strings for UUID fields
      const cleanedData = {
        ...data,
        guardian_id: data.guardian_id && data.guardian_id.trim() !== '' ? data.guardian_id : undefined,
        phone: data.phone && data.phone.trim() !== '' ? data.phone : undefined,
        insurance_expiry: data.insurance_expiry && data.insurance_expiry.trim() !== '' ? data.insurance_expiry : undefined,
        licence_number: data.licence_number && data.licence_number.trim() !== '' ? data.licence_number : undefined,
      };

      let result;
      if (student) {
        result = await studentService.updateStudent(student.id, cleanedData);
      } else {
        result = await studentService.createStudent(cleanedData);
      }

      if (result.error) {
        setError(result.error);
      } else if (result.data) {
        onSave(result.data);
      }
    } catch (err) {
      setError('Failed to save student');
    } finally {
      setLoading(false);
    }
  };

  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return '';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return age - 1;
    }
    return age;
  };

  const dateOfBirth = watch('date_of_birth');

  return (
    <div className="max-w-4xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
      <div className="px-6 py-4 bg-indigo-600 text-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <User className="h-6 w-6" />
            <h2 className="text-xl font-semibold">
              {student ? 'Edit Student' : 'Add New Student'}
            </h2>
          </div>
          <button
            onClick={onCancel}
            className="text-indigo-200 hover:text-white"
          >
            <X className="h-6 w-6" />
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-8">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="text-sm text-red-600">{error}</div>
          </div>
        )}

        {/* Personal Information */}
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <User className="h-5 w-5 mr-2 text-indigo-600" />
            Personal Information
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                First Name *
              </label>
              <input
                type="text"
                {...register('first_name', { required: 'First name is required' })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              {errors.first_name && (
                <p className="mt-1 text-sm text-red-600">{errors.first_name.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Last Name *
              </label>
              <input
                type="text"
                {...register('last_name', { required: 'Last name is required' })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              {errors.last_name && (
                <p className="mt-1 text-sm text-red-600">{errors.last_name.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date of Birth *
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="date"
                  {...register('date_of_birth', { required: 'Date of birth is required' })}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
              {dateOfBirth && (
                <p className="mt-1 text-sm text-gray-500">Age: {calculateAge(dateOfBirth)} years</p>
              )}
              {errors.date_of_birth && (
                <p className="mt-1 text-sm text-red-600">{errors.date_of_birth.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone Number
              </label>
              <div className="relative">
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="tel"
                  {...register('phone')}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Martial Arts Information */}
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Shield className="h-5 w-5 mr-2 text-indigo-600" />
            Martial Arts Information
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Current Belt Color *
              </label>
              <select
                {...register('belt_color', { required: 'Belt color is required' })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                {beltColors.map(color => (
                  <option key={color} value={color}>
                    {color.charAt(0).toUpperCase() + color.slice(1)} Belt
                  </option>
                ))}
              </select>
              {errors.belt_color && (
                <p className="mt-1 text-sm text-red-600">{errors.belt_color.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Insurance Expiry
              </label>
              <input
                type="date"
                {...register('insurance_expiry')}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Licence Number
              </label>
              <input
                type="text"
                {...register('licence_number')}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
          </div>
        </div>

        {/* Guardian Information */}
        {guardians.length > 0 && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Guardian Information</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Guardian
              </label>
              <select
                {...register('guardian_id')}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">No Guardian</option>
                {guardians.map(guardian => (
                  <option key={guardian.id} value={guardian.id}>
                    {guardian.first_name} {guardian.last_name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        )}

        {/* Consent and Medical */}
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Heart className="h-5 w-5 mr-2 text-indigo-600" />
            Consent & Medical Information
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('consent_social_media')}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Consent to social media usage (photos/videos)
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('consent_assumption_risk')}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Assumption of risk consent
              </label>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            {student ? 'Update Student' : 'Create Student'}
          </button>
        </div>
      </form>
    </div>
  );
};
