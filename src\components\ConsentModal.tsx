import React, { useState, useEffect, useRef } from 'react';
import { X, CheckCircle, AlertTriangle } from 'lucide-react';

interface ConsentModalProps {
  type: 'social' | 'risk';
  onClose: () => void;
  onRead: () => void;
}

const SOCIAL_MEDIA_POLICY = `
SOCIAL MEDIA AND PHOTOGRAPHY CONSENT POLICY

By providing consent, you acknowledge and agree to the following:

1. PHOTOGRAPHY AND VIDEOGRAPHY
   • The martial arts school may take photographs and videos during classes, events, and competitions
   • These images may include your child participating in training activities
   • Photos and videos may be taken by instructors, staff, or authorized personnel

2. <PERSON>GE RIGHTS
   • Images may be used for promotional materials including:
     - Website content and galleries
     - Social media posts (Facebook, Instagram, Twitter, etc.)
     - Marketing brochures and flyers
     - Newsletter content
     - Competition documentation

3. PRIVACY CONSIDERATIONS
   • We will not use images in a way that could be considered inappropriate or harmful
   • Personal information (full names, addresses) will not be shared alongside images
   • We respect your child's dignity and will only use images that portray them positively

4. WITHDRAWAL OF CONSENT
   • You may withdraw this consent at any time by contacting the school
   • Upon withdrawal, we will make reasonable efforts to remove existing content
   • Future photography/videography of your child will be avoided

5. NO COMPENSATION
   • No payment or compensation will be provided for the use of these images
   • The school retains rights to use approved images indefinitely unless consent is withdrawn

By checking the consent box, you confirm that you have read, understood, and agree to these terms.
`;

const ASSUMPTION_OF_RISK_POLICY = `
ASSUMPTION OF RISK AND WAIVER OF LIABILITY

PLEASE READ CAREFULLY - THIS IS A LEGAL DOCUMENT

By providing consent, you acknowledge and agree to the following:

1. INHERENT RISKS OF MARTIAL ARTS
   • Martial arts training involves physical contact and athletic activity
   • Risks include but are not limited to:
     - Bruises, cuts, and minor injuries
     - Sprains, strains, and muscle soreness
     - Potential for more serious injury despite safety precautions
     - Risk of injury from equipment or training partners

2. ASSUMPTION OF RISK
   • You voluntarily assume all risks associated with martial arts training
   • You understand that injuries can occur despite proper instruction and safety measures
   • You acknowledge that your child's participation is voluntary

3. MEDICAL CONSIDERATIONS
   • You confirm your child is physically capable of participating
   • You will inform instructors of any medical conditions or limitations
   • You understand the importance of following safety instructions

4. SAFETY MEASURES
   • The school maintains safety protocols and qualified instruction
   • Protective equipment is recommended and may be required
   • Students are expected to follow all safety rules and instructor guidance

5. EMERGENCY MEDICAL TREATMENT
   • You authorize emergency medical treatment if needed during training
   • You are responsible for all medical costs resulting from injuries
   • The school will make reasonable efforts to contact you in case of injury

6. WAIVER OF LIABILITY
   • You waive claims against the school, instructors, and staff for injuries
   • This waiver applies to negligence but not gross negligence or intentional acts
   • You agree to hold the school harmless from claims by third parties

7. INSURANCE
   • You are responsible for maintaining adequate health insurance
   • The school's insurance does not cover student injuries
   • You should verify your insurance covers martial arts activities

By checking the consent box, you confirm that you have read, understood, and voluntarily agree to assume these risks and waive liability as described above.

This agreement is binding and affects your legal rights. If you do not understand any portion, please seek legal advice before providing consent.
`;

export const ConsentModal: React.FC<ConsentModalProps> = ({ type, onClose, onRead }) => {
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);
  const [showConfirmButton, setShowConfirmButton] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  const policy = type === 'social' ? SOCIAL_MEDIA_POLICY : ASSUMPTION_OF_RISK_POLICY;
  const title = type === 'social' 
    ? 'Social Media and Photography Consent Policy' 
    : 'Assumption of Risk and Waiver of Liability';

  useEffect(() => {
    // Reset state when modal opens
    setHasScrolledToBottom(false);
    setShowConfirmButton(false);
  }, [type]);

  const handleScroll = () => {
    if (scrollRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10; // 10px tolerance
      
      if (isAtBottom && !hasScrolledToBottom) {
        setHasScrolledToBottom(true);
        // Show confirm button after a short delay to ensure user has time to read
        setTimeout(() => setShowConfirmButton(true), 1000);
      }
    }
  };

  const handleConfirm = () => {
    onRead();
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        ></div>

        {/* Modal */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          {/* Header */}
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-start justify-between">
              <div className="flex items-center">
                {type === 'risk' ? (
                  <AlertTriangle className="h-6 w-6 text-red-500 mr-3" />
                ) : (
                  <CheckCircle className="h-6 w-6 text-blue-500 mr-3" />
                )}
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  {title}
                </h3>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="px-4 pb-4 sm:px-6">
            <div 
              ref={scrollRef}
              onScroll={handleScroll}
              className="max-h-96 overflow-y-auto border border-gray-200 rounded-md p-4 bg-gray-50"
            >
              <div className="whitespace-pre-line text-sm text-gray-700 leading-relaxed">
                {policy}
              </div>
            </div>

            {/* Scroll instruction */}
            {!hasScrolledToBottom && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex items-center">
                  <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2" />
                  <p className="text-sm text-yellow-700">
                    Please scroll through the entire policy to enable the consent option.
                  </p>
                </div>
              </div>
            )}

            {/* Confirmation */}
            {hasScrolledToBottom && (
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  <p className="text-sm text-green-700">
                    You have read through the policy. You can now provide consent.
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            {showConfirmButton ? (
              <button
                onClick={handleConfirm}
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
              >
                I Have Read and Understand
              </button>
            ) : (
              <button
                disabled
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-gray-300 text-base font-medium text-gray-500 cursor-not-allowed sm:ml-3 sm:w-auto sm:text-sm"
              >
                Read Policy First
              </button>
            )}
            
            <button
              onClick={onClose}
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
