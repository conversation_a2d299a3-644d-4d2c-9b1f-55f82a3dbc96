import React, { useState } from 'react';
import { StudentList } from '../components/StudentList';
import { StudentForm } from '../components/StudentForm';
import { StudentDetail } from '../components/StudentDetail';
import type { Student } from '../types';

type ViewMode = 'list' | 'create' | 'edit' | 'detail';

export const StudentsPage: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);

  const handleCreateStudent = () => {
    setSelectedStudent(null);
    setViewMode('create');
  };

  const handleEditStudent = (student: Student) => {
    setSelectedStudent(student);
    setViewMode('edit');
  };

  const handleSelectStudent = (student: Student) => {
    setSelectedStudent(student);
    setViewMode('detail');
  };

  const handleSaveStudent = (student: Student) => {
    setSelectedStudent(student);
    setViewMode('detail');
  };

  const handleCancel = () => {
    setSelectedStudent(null);
    setViewMode('list');
  };

  const handleBackToList = () => {
    setSelectedStudent(null);
    setViewMode('list');
  };

  const renderContent = () => {
    switch (viewMode) {
      case 'create':
        return (
          <StudentForm
            onSave={handleSaveStudent}
            onCancel={handleCancel}
          />
        );
      
      case 'edit':
        return selectedStudent ? (
          <StudentForm
            student={selectedStudent}
            onSave={handleSaveStudent}
            onCancel={handleCancel}
          />
        ) : null;
      
      case 'detail':
        return selectedStudent ? (
          <StudentDetail
            student={selectedStudent}
            onEdit={() => handleEditStudent(selectedStudent)}
            onClose={handleBackToList}
          />
        ) : null;
      
      case 'list':
      default:
        return (
          <StudentList
            onSelectStudent={handleSelectStudent}
            onEditStudent={handleEditStudent}
            onCreateStudent={handleCreateStudent}
            showActions={true}
          />
        );
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {renderContent()}
    </div>
  );
};
