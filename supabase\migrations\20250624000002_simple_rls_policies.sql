-- Drop the problematic function and create simpler policies
DROP FUNCTION IF EXISTS auth.user_role();

-- Temporarily disable <PERSON><PERSON> on profiles to fix recursion issue
-- We'll re-enable it later with proper policies
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies on profiles
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
DROP POLICY IF EXISTS "Instructors and admins can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON profiles;
DROP POLICY IF EXISTS "Enable update for users based on email" ON profiles;

-- Students policies (simplified)
DROP POLICY IF EXISTS "Instructors and admins can manage students" ON students;
DROP POLICY IF EXISTS "Users can view linked students" ON students;

-- Allow instructors and admins to manage students (we'll check role in application)
CREATE POLICY "Authenticated users can manage students" ON students
  FOR ALL USING (auth.role() = 'authenticated');

-- Sessions policies (simplified)
DROP POLICY IF EXISTS "Instructors and admins can manage sessions" ON sessions;
DROP POLICY IF EXISTS "All authenticated users can view sessions" ON sessions;

CREATE POLICY "Authenticated users can manage sessions" ON sessions
  FOR ALL USING (auth.role() = 'authenticated');

-- Attendance policies (simplified)
DROP POLICY IF EXISTS "Instructors and admins can manage attendance" ON attendance;
DROP POLICY IF EXISTS "Users can view attendance for linked students" ON attendance;

CREATE POLICY "Authenticated users can manage attendance" ON attendance
  FOR ALL USING (auth.role() = 'authenticated');

-- Syllabus policies (simplified)
DROP POLICY IF EXISTS "All authenticated users can view syllabus" ON syllabus;
DROP POLICY IF EXISTS "Admins can manage syllabus" ON syllabus;

CREATE POLICY "Authenticated users can access syllabus" ON syllabus
  FOR ALL USING (auth.role() = 'authenticated');

-- Gradings policies (simplified)
DROP POLICY IF EXISTS "Instructors and admins can manage gradings" ON gradings;
DROP POLICY IF EXISTS "Users can view gradings for linked students" ON gradings;

CREATE POLICY "Authenticated users can manage gradings" ON gradings
  FOR ALL USING (auth.role() = 'authenticated');

-- Exercises policies (simplified)
DROP POLICY IF EXISTS "Instructors and admins can manage exercises" ON exercises;
DROP POLICY IF EXISTS "All authenticated users can view exercises" ON exercises;

CREATE POLICY "Authenticated users can manage exercises" ON exercises
  FOR ALL USING (auth.role() = 'authenticated');

-- Fitness records policies (simplified)
DROP POLICY IF EXISTS "Instructors and admins can manage fitness records" ON fitness_records;
DROP POLICY IF EXISTS "Users can view fitness records for linked students" ON fitness_records;

CREATE POLICY "Authenticated users can manage fitness records" ON fitness_records
  FOR ALL USING (auth.role() = 'authenticated');

-- Account link requests policies (simplified)
DROP POLICY IF EXISTS "Users can manage their own requests" ON account_link_requests;
DROP POLICY IF EXISTS "Instructors and admins can manage all requests" ON account_link_requests;

CREATE POLICY "Authenticated users can manage account link requests" ON account_link_requests
  FOR ALL USING (auth.role() = 'authenticated');
