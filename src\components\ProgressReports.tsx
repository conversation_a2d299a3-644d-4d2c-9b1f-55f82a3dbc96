import React, { useState, useEffect } from 'react';
import { BarChart, TrendingUp, Calendar, Award, Activity, User, Download } from 'lucide-react';
import { supabase } from '../services/supabaseClient';
import { studentService } from '../services';
import { useAuth } from '../contexts/AuthContext';

interface Student {
  id: string;
  first_name: string;
  last_name: string;
  belt_color: string;
  date_of_birth: string;
}

interface ProgressData {
  attendanceRate: number;
  attendanceHistory: Array<{ month: string; attended: number; total: number }>;
  gradingProgress: {
    mastered: number;
    inProgress: number;
    needsImprovement: number;
    notStarted: number;
    total: number;
  };
  fitnessProgress: Array<{
    exercise: string;
    personalBest: number;
    unit: string;
    improvement: number;
  }>;
  recentAchievements: Array<{
    type: 'grading' | 'fitness' | 'attendance';
    description: string;
    date: string;
  }>;
}

export const ProgressReports: React.FC = () => {
  const { state } = useAuth();
  const { profile } = state;
  const [students, setStudents] = useState<Student[]>([]);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [progressData, setProgressData] = useState<ProgressData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<'3months' | '6months' | '1year'>('6months');

  useEffect(() => {
    loadStudents();
  }, [profile]);

  useEffect(() => {
    if (selectedStudent) {
      loadProgressData();
    }
  }, [selectedStudent, dateRange]);

  const loadStudents = async () => {
    if (!profile) return;

    setLoading(true);
    setError(null);

    try {
      let studentsData: Student[] = [];

      if (profile.role === 'instructor' || profile.role === 'admin') {
        // Instructors can see all students
        const result = await studentService.getStudents({ active: true });
        if (result.error) {
          setError(result.error);
          return;
        }
        studentsData = result.data;
      } else if (profile.role === 'guardian') {
        // Guardians can only see their linked students
        const result = await studentService.getStudents({ guardian_id: profile.id });
        if (result.error) {
          setError(result.error);
          return;
        }
        studentsData = result.data;
      } else if (profile.role === 'student') {
        // Students can only see their own progress
        // Use .maybeSingle() instead of .single() to handle the case where no row is found
        const { data, error } = await supabase
          .from('students')
          .select('*')
          .eq('user_id', profile.id);

        if (error) {
          setError(error.message);
          return;
        }
        if (data && data.length > 0) {
          studentsData = data;
        }
      }

      setStudents(studentsData);
      if (studentsData.length === 1) {
        setSelectedStudent(studentsData[0]);
      }
    } catch (err) {
      setError('Failed to load students');
    } finally {
      setLoading(false);
    }
  };

  const loadProgressData = async () => {
    if (!selectedStudent) return;

    setLoading(true);
    setError(null);

    try {
      const endDate = new Date();
      const startDate = new Date();
      
      switch (dateRange) {
        case '3months':
          startDate.setMonth(endDate.getMonth() - 3);
          break;
        case '6months':
          startDate.setMonth(endDate.getMonth() - 6);
          break;
        case '1year':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
      }

      // Load attendance data
      const { data: attendanceData, error: attendanceError } = await supabase
        .from('attendance')
        .select(`
          *,
          session:session_id(date)
        `)
        .eq('student_id', selectedStudent.id)
        .gte('recorded_at', startDate.toISOString())
        .lte('recorded_at', endDate.toISOString());

      if (attendanceError) throw attendanceError;

      // Load grading data
      const { data: gradingData, error: gradingError } = await supabase
        .from('gradings')
        .select('*')
        .eq('student_id', selectedStudent.id);

      if (gradingError) throw gradingError;

      // Load fitness records
      const { data: fitnessData, error: fitnessError } = await supabase
        .from('exercise_logs')
        .select(`
          *,
          exercise:exercise_id(name),
          measurements:exercise_log_measurements(*)
        `)
        .eq('student_id', selectedStudent.id)
        .gte('session_date', startDate.toISOString().split('T')[0])
        .lte('session_date', endDate.toISOString().split('T')[0]);

      if (fitnessError) throw fitnessError;

      // Process attendance history
      const attendanceHistory = processAttendanceHistory(attendanceData || [], startDate, endDate);
      
      // Calculate attendance rate
      const totalAttended = (attendanceData || []).filter(a => 
        a.status === 'attended_paid' || a.status === 'attended_unpaid'
      ).length;
      const totalSessions = (attendanceData || []).length;
      const attendanceRate = totalSessions > 0 ? Math.round((totalAttended / totalSessions) * 100) : 0;

      // Process grading progress
      const gradingProgress = processGradingProgress(gradingData || []);

      // Process fitness progress
      const fitnessProgress = processFitnessProgress(fitnessData || []);

      // Generate recent achievements
      const recentAchievements = generateRecentAchievements(
        attendanceData || [],
        gradingData || [],
        fitnessData || []
      );

      setProgressData({
        attendanceRate,
        attendanceHistory,
        gradingProgress,
        fitnessProgress,
        recentAchievements
      });
    } catch (err: any) {
      setError(err.message || 'Failed to load progress data');
    } finally {
      setLoading(false);
    }
  };

  const processAttendanceHistory = (attendanceData: any[], startDate: Date, endDate: Date) => {
    const months: Array<{ month: string; attended: number; total: number }> = [];
    const current = new Date(startDate);
    
    while (current <= endDate) {
      const monthKey = current.toISOString().slice(0, 7); // YYYY-MM
      const monthName = current.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
      
      const monthAttendance = attendanceData.filter(a => 
        a.recorded_at.startsWith(monthKey)
      );
      
      const attended = monthAttendance.filter(a => 
        a.status === 'attended_paid' || a.status === 'attended_unpaid'
      ).length;
      
      months.push({
        month: monthName,
        attended,
        total: monthAttendance.length
      });
      
      current.setMonth(current.getMonth() + 1);
    }
    
    return months;
  };

  const processGradingProgress = (gradingData: any[]) => {
    const mastered = gradingData.filter(g => g.status === 'mastered').length;
    const inProgress = gradingData.filter(g => g.status === 'in_progress').length;
    const needsImprovement = gradingData.filter(g => g.status === 'needs_improvement').length;
    const notStarted = gradingData.filter(g => g.status === 'not_started').length;
    
    return {
      mastered,
      inProgress,
      needsImprovement,
      notStarted,
      total: gradingData.length
    };
  };

  const processFitnessProgress = (fitnessData: any[]) => {
    const exerciseGroups = fitnessData.reduce((acc, log) => {
      const exerciseName = log.exercise?.name || 'Unknown';
      if (!acc[exerciseName]) {
        acc[exerciseName] = [];
      }
      acc[exerciseName].push(log);
      return acc;
    }, {} as Record<string, any[]>);

    return Object.entries(exerciseGroups).map(([exercise, logs]) => {
      // Find the best performance for primary measurement
      const measurements = logs.flatMap(log => log.measurements || []);
      const primaryMeasurements = measurements.filter(m => 
        m.measurement_type === 'reps' || m.measurement_type === 'time' || m.measurement_type === 'distance'
      );

      if (primaryMeasurements.length === 0) {
        return { exercise, personalBest: 0, unit: '', improvement: 0 };
      }

      const bestMeasurement = primaryMeasurements.reduce((best, current) => {
        if (current.measurement_type === 'time') {
          return current.value < best.value ? current : best;
        } else {
          return current.value > best.value ? current : best;
        }
      });

      // Calculate improvement (simplified)
      const firstMeasurement = primaryMeasurements[0];
      const improvement = bestMeasurement.value - firstMeasurement.value;

      return {
        exercise,
        personalBest: bestMeasurement.value,
        unit: bestMeasurement.unit,
        improvement: Math.round(improvement * 100) / 100
      };
    });
  };

  const generateRecentAchievements = (attendanceData: any[], gradingData: any[], fitnessData: any[]) => {
    const achievements: Array<{
      type: 'grading' | 'fitness' | 'attendance';
      description: string;
      date: string;
    }> = [];

    // Recent mastered skills
    gradingData
      .filter(g => g.status === 'mastered' && g.graded_at)
      .sort((a, b) => new Date(b.graded_at).getTime() - new Date(a.graded_at).getTime())
      .slice(0, 3)
      .forEach(grading => {
        achievements.push({
          type: 'grading',
          description: `Mastered a new skill`,
          date: grading.graded_at
        });
      });

    // Recent fitness records
    fitnessData
      .filter(log => log.verification_status === 'instructor_verified')
      .sort((a, b) => new Date(b.session_date).getTime() - new Date(a.session_date).getTime())
      .slice(0, 2)
      .forEach(log => {
        achievements.push({
          type: 'fitness',
          description: `Completed ${log.exercise?.name || 'exercise'}`,
          date: log.session_date
        });
      });

    // Perfect attendance streaks
    const recentAttendance = attendanceData
      .filter(a => a.status === 'attended_paid' || a.status === 'attended_unpaid')
      .sort((a, b) => new Date(b.recorded_at).getTime() - new Date(a.recorded_at).getTime());

    if (recentAttendance.length >= 5) {
      achievements.push({
        type: 'attendance',
        description: `Excellent attendance record`,
        date: recentAttendance[0].recorded_at
      });
    }

    return achievements
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 5);
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return age - 1;
    }
    return age;
  };

  const getAchievementIcon = (type: string) => {
    switch (type) {
      case 'grading':
        return <Award className="h-4 w-4 text-yellow-500" />;
      case 'fitness':
        return <Activity className="h-4 w-4 text-green-500" />;
      case 'attendance':
        return <Calendar className="h-4 w-4 text-blue-500" />;
      default:
        return <TrendingUp className="h-4 w-4 text-gray-500" />;
    }
  };

  if (loading && !selectedStudent) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <BarChart className="h-6 w-6 text-indigo-600 mr-3" />
            Progress Reports
          </h2>
          
          <div className="flex items-center space-x-4">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="3months">Last 3 Months</option>
              <option value="6months">Last 6 Months</option>
              <option value="1year">Last Year</option>
            </select>
            
            <button className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <Download className="h-4 w-4 mr-2" />
              Export
            </button>
          </div>
        </div>

        {students.length > 1 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Select Student
            </label>
            <select
              value={selectedStudent?.id || ''}
              onChange={(e) => {
                const student = students.find(s => s.id === e.target.value);
                setSelectedStudent(student || null);
              }}
              className="block w-full max-w-md px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">Select a student</option>
              {students.map(student => (
                <option key={student.id} value={student.id}>
                  {student.first_name} {student.last_name} ({student.belt_color} belt)
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-600">{error}</div>
        </div>
      )}

      {selectedStudent && progressData && (
        <>
          {/* Student Overview */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center space-x-4 mb-6">
              <div className="h-16 w-16 rounded-full bg-indigo-100 flex items-center justify-center">
                <span className="text-xl font-medium text-indigo-600">
                  {selectedStudent.first_name[0]}{selectedStudent.last_name[0]}
                </span>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900">
                  {selectedStudent.first_name} {selectedStudent.last_name}
                </h3>
                <p className="text-sm text-gray-500">
                  Age: {calculateAge(selectedStudent.date_of_birth)} • {selectedStudent.belt_color.charAt(0).toUpperCase() + selectedStudent.belt_color.slice(1)} Belt
                </p>
              </div>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-indigo-600">{progressData.attendanceRate}%</div>
                <div className="text-sm text-gray-600">Attendance Rate</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">{progressData.gradingProgress.mastered}</div>
                <div className="text-sm text-gray-600">Skills Mastered</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">{progressData.fitnessProgress.length}</div>
                <div className="text-sm text-gray-600">Exercises Completed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">{progressData.recentAchievements.length}</div>
                <div className="text-sm text-gray-600">Recent Achievements</div>
              </div>
            </div>
          </div>

          {/* Charts and Progress */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Attendance Chart */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Attendance History</h3>
              <div className="space-y-3">
                {progressData.attendanceHistory.map((month, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">{month.month}</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-indigo-600 h-2 rounded-full" 
                          style={{ 
                            width: month.total > 0 ? `${(month.attended / month.total) * 100}%` : '0%' 
                          }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-900 w-16 text-right">
                        {month.attended}/{month.total}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Grading Progress */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Skill Progress</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Mastered</span>
                  <span className="text-sm font-medium text-green-600">{progressData.gradingProgress.mastered}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">In Progress</span>
                  <span className="text-sm font-medium text-yellow-600">{progressData.gradingProgress.inProgress}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Needs Improvement</span>
                  <span className="text-sm font-medium text-red-600">{progressData.gradingProgress.needsImprovement}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Not Started</span>
                  <span className="text-sm font-medium text-gray-600">{progressData.gradingProgress.notStarted}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Fitness Progress & Achievements */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Fitness Progress */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Fitness Progress</h3>
              {progressData.fitnessProgress.length === 0 ? (
                <p className="text-sm text-gray-500">No fitness records available</p>
              ) : (
                <div className="space-y-3">
                  {progressData.fitnessProgress.slice(0, 5).map((exercise, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-gray-900">{exercise.exercise}</span>
                      <div className="text-right">
                        <div className="text-sm font-medium text-gray-900">
                          {exercise.personalBest} {exercise.unit}
                        </div>
                        {exercise.improvement !== 0 && (
                          <div className={`text-xs ${exercise.improvement > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {exercise.improvement > 0 ? '+' : ''}{exercise.improvement} {exercise.unit}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Recent Achievements */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Achievements</h3>
              {progressData.recentAchievements.length === 0 ? (
                <p className="text-sm text-gray-500">No recent achievements</p>
              ) : (
                <div className="space-y-3">
                  {progressData.recentAchievements.map((achievement, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-0.5">
                        {getAchievementIcon(achievement.type)}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm text-gray-900">{achievement.description}</p>
                        <p className="text-xs text-gray-500">
                          {new Date(achievement.date).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};
