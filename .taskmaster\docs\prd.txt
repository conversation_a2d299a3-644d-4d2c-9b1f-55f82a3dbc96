# Martial Arts CRM - Product Requirements Document (PRD)

## Overview
A CRM platform for martial arts schools, supporting student management, attendance, grading, fitness tracking, and account linking, built with React, Supabase, and modern TypeScript tooling.

## Tech Stack
- Frontend: React 18+ (TypeScript)
- Styling: Tailwind CSS
- Backend: Supabase (PostgreSQL, Auth, Storage, Real-time)
- State Management: React Context + useReducer
- Forms: React Hook Form + Zod
- Routing: React Router v6
- Date Handling: date-fns
- Icons: Lucide React

## Core Features
1. **Authentication & Profiles**
   - User sign up/in/out (Supabase Auth)
   - Profile management (first/last name, DOB, phone, address, avatar, role)
   - Roles: admin, instructor, student, guardian
   - Row-level security for profile data

2. **Student Management**
   - CRUD for student records (can exist without user accounts)
   - Emergency contacts, medical conditions, belt color, insurance, guardian linking
   - Real-time updates for student data

3. **Account Linking**
   - Search for students by name + DOB
   - Submit link requests (self or guardian)
   - Instructor review/approval workflow
   - On approval, link user/guardian to student record
   - Permissions update accordingly

4. **Session & Attendance**
   - Create/manage sessions (name, date, time, instructor, capacity, type)
   - Mark attendance (attended_paid, attended_unpaid, absent)
   - Only instructors can mark attendance
   - One attendance record per student per session

5. **Syllabus & Grading**
   - Syllabus items grouped by belt color/category/skill
   - Record grading results (pass, fail, not_assessed)
   - Only instructors can record grades
   - One grading record per student-skill

6. **Fitness Tracking**
   - Define exercises (count, time, weight, distance)
   - Record fitness results (self or instructor-verified)
   - Leaderboards and progress tracking

7. **UI/UX**
   - Responsive layout with sidebar/header
   - Protected routes by role
   - Student list/search, add/edit forms, validation
   - Use Lucide icons, Tailwind for styling

## Database Schema
- Profiles, Students, Account Link Requests, Sessions, Attendance, Syllabus, Gradings, Exercises, Fitness Records
- Row-level security and policies as per provided SQL
- Triggers for updated_at columns

## TypeScript Types
- As per provided types for Profile, Student, AccountLinkRequest, Session, Attendance, SyllabusItem, Grading, Exercise, FitnessRecord, Address, EmergencyContact, MedicalCondition

## Key Business Logic
- Account linking: search, request, instructor approval, link, permission update
- Attendance: instructor-only, one record per session/student, status values
- Grading: grouped by belt, one record per student-skill, instructor-only
- Fitness: measurement types, verification, leaderboards, progress

## Implementation Notes
- Use React Context + useReducer for global state (auth, profile)
- Use React Hook Form + Zod for all forms
- Use Supabase client for all data access
- Real-time subscriptions for students
- Modular service layer for API calls
- All sensitive actions protected by RLS and UI role checks

## Out of Scope
- Payment processing
- External calendar integration
- Mobile app (web only for MVP)

## Acceptance Criteria
- All core features implemented with working UI
- Database schema matches provided SQL
- RLS and policies enforced
- All forms validated and error-handled
- Real-time updates for student data
- Instructor/admin-only actions enforced
- All business logic rules implemented

# Technical Architecture  
- **Frontend**: React 18+ (TypeScript), Tailwind CSS, React Context + useReducer, React Hook Form + Zod, React Router v6, date-fns, Lucide React.
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Real-time, RLS policies).
- **APIs**: Supabase client for all CRUD and auth operations. Service layer abstracts API calls.
- **Data Models**: Profiles, Students, AccountLinkRequests, Sessions, Attendance, Syllabus, Gradings, Exercises, FitnessRecords (see schema).
- **Infrastructure**: Supabase project, environment variables for keys, secure RLS policies.

# Development Roadmap  
- **MVP**:
  - User authentication and profile management
  - Student CRUD and account linking
  - Session scheduling and attendance
  - Grading and syllabus tracking
  - Fitness tracking (basic)
  - Role-based permissions
  - Real-time updates for students/attendance
- **Future Enhancements**:
  - Notifications (email/SMS)
  - Advanced analytics and reporting
  - Payment integration
  - Mobile app
  - Customizable forms and workflows

# Logical Dependency Chain
- Set up Supabase project, tables, and RLS policies
- Implement authentication and profile context
- Build student management and account linking
- Add session scheduling and attendance
- Integrate grading and syllabus tracking
- Add fitness tracking
- Layer in real-time subscriptions
- Enhance UI/UX and error handling

# Risks and Mitigations  
- **Technical**: RLS policy misconfiguration (mitigate with thorough testing), real-time sync issues (test with Supabase channels), data consistency (enforce unique constraints).
- **MVP Scope**: Focus on core flows first, defer advanced features.
- **Resources**: Use open-source stack and Supabase free tier for early development.

# Appendix  
- See full database schema and TypeScript types in the technical specification above. 