import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { signUp, getErrorMessage, createInitialProfile } from '../services/authService';
import { useAuth } from '../contexts/AuthContext';
import { UserPlus, CheckCircle, X, AlertTriangle, Shield } from 'lucide-react';
import { PageCenter } from '../components/PageLayout';

const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

const schema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character'),
  confirmPassword: z.string(),
  role: z.enum(['student', 'instructor', 'guardian']),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

type SignUpFormData = z.infer<typeof schema>;

export const SignUpPage: React.FC = () => {
  const { dispatch } = useAuth();
  const [errorMsg, setErrorMsg] = useState('');
  const [success, setSuccess] = useState(false);
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
    reset,
    watch,
  } = useForm<SignUpFormData>({ resolver: zodResolver(schema), defaultValues: { role: 'student' } });
  
  const password = watch('password');
  const [passwordStrength, setPasswordStrength] = useState(0);
  // Password strength calculation
  useEffect(() => {
    if (password) {
      let strength = 0;
      // Length check
      if (password.length >= 6) strength += 20;
      if (password.length >= 10) strength += 10;
      // Character variety check
      if (/[A-Z]/.test(password)) strength += 20; // Uppercase
      if (/[a-z]/.test(password)) strength += 10; // Lowercase
      if (/[0-9]/.test(password)) strength += 20; // Numbers
      if (/[^A-Za-z0-9]/.test(password)) strength += 20; // Special characters
      setPasswordStrength(strength);
    } else {
      setPasswordStrength(0);
    }
  }, [password]);
  
  const onSubmit = async (data: SignUpFormData) => {
    setErrorMsg('');
    setSuccess(false);
    const { error, data: result } = await signUp(data.email, data.password);
    if (error) {
      setError('email', { message: getErrorMessage(error) });
      setErrorMsg(getErrorMessage(error));
      return;
    }
    
    if (result?.user) {
      // Use our robust profile creation function with retry mechanism
      const { error: profileError } = await createInitialProfile(
        result.user.id,
        data.firstName,
        data.lastName,
        data.role
      );
      
      if (profileError) {
        console.error('Profile creation error:', profileError);
        setErrorMsg(`Account created but profile setup failed: ${getErrorMessage(profileError)}`);
        // Still consider signup successful, they can complete profile later
      }
      
      dispatch({ type: 'LOGIN', payload: result.user });
      setSuccess(true);
      reset();
      
      // Redirect after a short delay to show success message
      setTimeout(() => {
        navigate('/complete-profile');
      }, 1500);
    }
  };
  
  return (
    <PageCenter maxWidth="max-w-lg">
      <div className="flex flex-col items-center mb-6">
        <UserPlus className="w-10 h-10 text-indigo-600 mb-2" />
        <h2 className="text-3xl font-extrabold text-indigo-700">Sign Up</h2>
      </div>
      
      {success ? (
        <div className="text-center mb-6">
          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-green-700 mb-2">Account Created!</h3>
          <p className="text-gray-600">
            You'll be redirected to complete your profile...
          </p>
        </div>
      ) : (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block mb-1 font-medium text-gray-700">First Name</label>
              <input
                type="text"
                {...register('firstName')}
                className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              />
              {errors.firstName && (
                <p className="text-red-600 text-sm mt-1">{errors.firstName.message}</p>
              )}
            </div>
            <div>
              <label className="block mb-1 font-medium text-gray-700">Last Name</label>
              <input
                type="text"
                {...register('lastName')}
                className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              />
              {errors.lastName && (
                <p className="text-red-600 text-sm mt-1">{errors.lastName.message}</p>
              )}
            </div>
          </div>
          <div>
            <label className="block mb-1 font-medium text-gray-700">Email</label>
            <input
              type="email"
              {...register('email')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              autoComplete="email"
            />
            {errors.email && <p className="text-red-600 text-sm mt-1">{errors.email.message}</p>}
          </div>
          
          <div>
            <label className="flex items-center mb-1 font-medium text-gray-700">
              <Shield className="w-4 h-4 mr-1" />
              Password
            </label>
            <input
              type="password"
              {...register('password')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              autoComplete="new-password"
            />
            {errors.password && <p className="text-red-600 text-sm mt-1">{errors.password.message}</p>}
            
            {/* Password strength indicator */}
            {password && (
              <div className="mt-2">
                <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                  <div 
                    className={`h-full ${
                      passwordStrength < 30 ? 'bg-red-500' : 
                      passwordStrength < 60 ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{width: `${Math.min(100, passwordStrength)}%`}}
                  ></div>
                </div>
                <div className="flex justify-between mt-1 text-xs">
                  <span className={passwordStrength >= 20 ? 'text-green-600' : 'text-gray-400'}>Length</span>
                  <span className={passwordStrength >= 40 ? 'text-green-600' : 'text-gray-400'}>Uppercase</span>
                  <span className={passwordStrength >= 60 ? 'text-green-600' : 'text-gray-400'}>Number</span>
                  <span className={passwordStrength >= 80 ? 'text-green-600' : 'text-gray-400'}>Symbol</span>
                </div>
              </div>
            )}
          </div>
          
          <div>
            <label className="block mb-1 font-medium text-gray-700">Confirm Password</label>
            <input
              type="password"
              {...register('confirmPassword')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              autoComplete="new-password"
            />
            {errors.confirmPassword && (
              <p className="text-red-600 text-sm mt-1">{errors.confirmPassword.message}</p>
            )}
          </div>
          
          <div>
            <label className="block mb-1 font-medium text-gray-700">Role</label>
            <select
              {...register('role')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400 bg-white"
            >
              <option value="student">Student</option>
              <option value="instructor">Instructor</option>
              <option value="guardian">Guardian</option>
            </select>
            {errors.role && <p className="text-red-600 text-sm mt-1">{errors.role.message}</p>}
          </div>
          
          <button
            type="submit"
            className="w-full bg-gradient-to-r from-indigo-600 to-blue-500 text-white py-2.5 rounded-lg font-bold text-lg shadow-md hover:from-indigo-700 hover:to-blue-600 transition mt-4"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Creating Account...' : 'Sign Up'}
          </button>
        </form>
      )}
      
      {errorMsg && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mt-6">
          <div className="flex items-center">
            <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
            <p className="text-red-700">{errorMsg}</p>
          </div>
        </div>
      )}
      
      <div className="text-center mt-6">
        <p className="text-gray-600">
          Already have an account?{' '}
          <Link to="/signin" className="text-indigo-600 hover:text-indigo-800 font-medium">
            Sign in
          </Link>
        </p>
      </div>
    </PageCenter>
  );
};
