import React, { useState, useEffect } from 'react';
import { User, Award, CheckCircle, Clock, X, Save, Target } from 'lucide-react';
import { supabase } from '../services/supabaseClient';
import { studentService } from '../services';
import { useAuth } from '../contexts/AuthContext';

interface Student {
  id: string;
  first_name: string;
  last_name: string;
  belt_color: string;
  date_of_birth: string;
}

interface SyllabusItem {
  id: string;
  belt_level: string;
  category: string;
  skill_name: string;
  description?: string;
  requirements?: string;
  difficulty_level: number;
  is_required: boolean;
  display_order: number;
}

interface StudentGrading {
  id?: string;
  student_id: string;
  syllabus_item_id: string;
  status: 'not_started' | 'in_progress' | 'mastered' | 'needs_improvement';
  instructor_notes?: string;
  graded_at?: string;
  graded_by?: string;
}

const BELT_LEVELS = [
  'white', 'yellow', 'orange', 'green', 'blue', 'purple', 'brown', 'black'
];

const STATUS_OPTIONS = [
  { value: 'not_started', label: 'Not Started', color: 'bg-gray-100 text-gray-800' },
  { value: 'in_progress', label: 'In Progress', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'needs_improvement', label: 'Needs Improvement', color: 'bg-red-100 text-red-800' },
  { value: 'mastered', label: 'Mastered', color: 'bg-green-100 text-green-800' }
];

export const StudentGradingInterface: React.FC = () => {
  const { state } = useAuth();
  const { profile } = state;
  const [students, setStudents] = useState<Student[]>([]);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [syllabusItems, setSyllabusItems] = useState<SyllabusItem[]>([]);
  const [gradings, setGradings] = useState<StudentGrading[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedBelt, setSelectedBelt] = useState<string>('');

  useEffect(() => {
    loadStudents();
  }, []);

  useEffect(() => {
    if (selectedStudent) {
      loadStudentData();
    }
  }, [selectedStudent]);

  const loadStudents = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await studentService.getStudents({ active: true });
      if (result.error) {
        setError(result.error);
      } else {
        setStudents(result.data);
      }
    } catch (err) {
      setError('Failed to load students');
    } finally {
      setLoading(false);
    }
  };

  const loadStudentData = async () => {
    if (!selectedStudent) return;

    setLoading(true);
    setError(null);

    try {
      // Load syllabus items for current belt and next belt
      const currentBeltIndex = BELT_LEVELS.indexOf(selectedStudent.belt_color);
      const relevantBelts = [selectedStudent.belt_color];
      if (currentBeltIndex < BELT_LEVELS.length - 1) {
        relevantBelts.push(BELT_LEVELS[currentBeltIndex + 1]);
      }

      const { data: syllabusData, error: syllabusError } = await supabase
        .from('syllabus')
        .select('*')
        .in('belt_level', relevantBelts)
        .order('belt_level')
        .order('category')
        .order('display_order');

      if (syllabusError) throw syllabusError;

      // Load existing gradings for this student
      const { data: gradingsData, error: gradingsError } = await supabase
        .from('gradings')
        .select('*')
        .eq('student_id', selectedStudent.id);

      if (gradingsError) throw gradingsError;

      setSyllabusItems(syllabusData || []);
      setGradings(gradingsData || []);
      setSelectedBelt(selectedStudent.belt_color);
    } catch (err: any) {
      setError(err.message || 'Failed to load student data');
    } finally {
      setLoading(false);
    }
  };

  const getGradingForItem = (syllabusItemId: string): StudentGrading => {
    const existing = gradings.find(g => g.syllabus_item_id === syllabusItemId);
    return existing || {
      student_id: selectedStudent!.id,
      syllabus_item_id: syllabusItemId,
      status: 'not_started'
    };
  };

  const updateGrading = (syllabusItemId: string, updates: Partial<StudentGrading>) => {
    setGradings(prev => {
      const existing = prev.find(g => g.syllabus_item_id === syllabusItemId);
      if (existing) {
        return prev.map(g => 
          g.syllabus_item_id === syllabusItemId 
            ? { ...g, ...updates }
            : g
        );
      } else {
        return [...prev, {
          student_id: selectedStudent!.id,
          syllabus_item_id: syllabusItemId,
          status: 'not_started',
          ...updates
        }];
      }
    });
  };

  const saveGradings = async () => {
    if (!selectedStudent || !profile) return;

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      // Prepare gradings data
      const gradingsToSave = gradings.map(grading => ({
        ...grading,
        graded_by: profile.id,
        graded_at: new Date().toISOString()
      }));

      // Delete existing gradings for this student
      await supabase
        .from('gradings')
        .delete()
        .eq('student_id', selectedStudent.id);

      // Insert new gradings
      const { error } = await supabase
        .from('gradings')
        .insert(gradingsToSave);

      if (error) throw error;

      setSuccess('Student gradings saved successfully!');
    } catch (err: any) {
      setError(err.message || 'Failed to save gradings');
    } finally {
      setSaving(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'mastered':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'in_progress':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'needs_improvement':
        return <X className="h-4 w-4 text-red-500" />;
      default:
        return <Target className="h-4 w-4 text-gray-400" />;
    }
  };

  const getProgressStats = () => {
    const currentBeltItems = syllabusItems.filter(item => item.belt_level === selectedStudent?.belt_color);
    const currentBeltGradings = gradings.filter(grading => {
      const item = syllabusItems.find(s => s.id === grading.syllabus_item_id);
      return item?.belt_level === selectedStudent?.belt_color;
    });

    const mastered = currentBeltGradings.filter(g => g.status === 'mastered').length;
    const inProgress = currentBeltGradings.filter(g => g.status === 'in_progress').length;
    const needsImprovement = currentBeltGradings.filter(g => g.status === 'needs_improvement').length;
    const notStarted = currentBeltItems.length - currentBeltGradings.length;

    return { total: currentBeltItems.length, mastered, inProgress, needsImprovement, notStarted };
  };

  const filteredSyllabusItems = syllabusItems.filter(item => 
    selectedBelt === '' || item.belt_level === selectedBelt
  );

  const groupedItems = filteredSyllabusItems.reduce((acc, item) => {
    const key = `${item.belt_level}-${item.category}`;
    if (!acc[key]) {
      acc[key] = {
        belt_level: item.belt_level,
        category: item.category,
        items: []
      };
    }
    acc[key].items.push(item);
    return acc;
  }, {} as Record<string, { belt_level: string; category: string; items: SyllabusItem[] }>);

  if (loading && !selectedStudent) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Student Selection */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Student Grading</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Select Student
            </label>
            <select
              value={selectedStudent?.id || ''}
              onChange={(e) => {
                const student = students.find(s => s.id === e.target.value);
                setSelectedStudent(student || null);
              }}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">Select a student</option>
              {students.map(student => (
                <option key={student.id} value={student.id}>
                  {student.first_name} {student.last_name} ({student.belt_color} belt)
                </option>
              ))}
            </select>
          </div>

          {selectedStudent && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Filter by Belt Level
              </label>
              <select
                value={selectedBelt}
                onChange={(e) => setSelectedBelt(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">All Available Levels</option>
                {BELT_LEVELS.filter(belt => {
                  const currentIndex = BELT_LEVELS.indexOf(selectedStudent.belt_color);
                  const beltIndex = BELT_LEVELS.indexOf(belt);
                  return beltIndex >= currentIndex && beltIndex <= currentIndex + 1;
                }).map(belt => (
                  <option key={belt} value={belt}>
                    {belt.charAt(0).toUpperCase() + belt.slice(1)} Belt
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </div>

      {selectedStudent && (
        <>
          {/* Progress Overview */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Progress Overview - {selectedStudent.first_name} {selectedStudent.last_name}
              </h3>
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
                {selectedStudent.belt_color.charAt(0).toUpperCase() + selectedStudent.belt_color.slice(1)} Belt
              </span>
            </div>

            {(() => {
              const stats = getProgressStats();
              return (
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
                    <div className="text-sm text-gray-600">Total Skills</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{stats.mastered}</div>
                    <div className="text-sm text-gray-600">Mastered</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">{stats.inProgress}</div>
                    <div className="text-sm text-gray-600">In Progress</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{stats.needsImprovement}</div>
                    <div className="text-sm text-gray-600">Needs Work</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-600">{stats.notStarted}</div>
                    <div className="text-sm text-gray-600">Not Started</div>
                  </div>
                </div>
              );
            })()}
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="text-sm text-red-600">{error}</div>
            </div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 rounded-md p-4">
              <div className="text-sm text-green-600">{success}</div>
            </div>
          )}

          {/* Syllabus Items */}
          <div className="space-y-6">
            {Object.values(groupedItems).map((group) => (
              <div key={`${group.belt_level}-${group.category}`} className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">
                    {group.belt_level.charAt(0).toUpperCase() + group.belt_level.slice(1)} Belt - {group.category}
                  </h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {group.items.map((item) => {
                    const grading = getGradingForItem(item.id);
                    return (
                      <div key={item.id} className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              {getStatusIcon(grading.status)}
                              <h4 className="text-sm font-medium text-gray-900">{item.skill_name}</h4>
                              {item.is_required && (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                  Required
                                </span>
                              )}
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                Level {item.difficulty_level}
                              </span>
                            </div>
                            
                            {item.description && (
                              <p className="text-sm text-gray-600 mb-2">{item.description}</p>
                            )}
                            
                            {item.requirements && (
                              <p className="text-sm text-gray-500 mb-3">
                                <strong>Requirements:</strong> {item.requirements}
                              </p>
                            )}

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label className="block text-xs font-medium text-gray-700 mb-1">
                                  Status
                                </label>
                                <select
                                  value={grading.status}
                                  onChange={(e) => updateGrading(item.id, { 
                                    status: e.target.value as any 
                                  })}
                                  className="block w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                >
                                  {STATUS_OPTIONS.map(option => (
                                    <option key={option.value} value={option.value}>
                                      {option.label}
                                    </option>
                                  ))}
                                </select>
                              </div>
                              
                              <div>
                                <label className="block text-xs font-medium text-gray-700 mb-1">
                                  Instructor Notes
                                </label>
                                <input
                                  type="text"
                                  value={grading.instructor_notes || ''}
                                  onChange={(e) => updateGrading(item.id, { 
                                    instructor_notes: e.target.value 
                                  })}
                                  className="block w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                  placeholder="Notes about progress..."
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              onClick={saveGradings}
              disabled={saving}
              className="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {saving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              {saving ? 'Saving...' : 'Save All Gradings'}
            </button>
          </div>
        </>
      )}
    </div>
  );
};
