-- Fix database structure issues

-- 1. Create user_role enum type if it doesn't exist
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('student', 'instructor', 'guardian', 'admin');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 2. Add missing columns to profiles table and handle the role enum conversion
DO $$ 
BEGIN
    -- Add email column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'email') THEN
        ALTER TABLE profiles ADD COLUMN email TEXT;
    END IF;
    
    -- Add first_name column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'first_name') THEN
        ALTER TABLE profiles ADD COLUMN first_name TEXT;
    END IF;
    
    -- Add last_name column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'last_name') THEN
        ALTER TABLE profiles ADD COLUMN last_name TEXT;
    END IF;
    
    -- Handle the enum conversion for 'role' column
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'role' AND data_type = 'text') THEN
        -- First, drop any existing constraints and dependent objects
        
        -- Drop existing RLS policies on profiles that reference the role column
        DROP POLICY IF EXISTS "Users can read own profile" ON profiles;
        DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
        DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
        DROP POLICY IF EXISTS "Service role can manage all profiles" ON profiles;
        DROP POLICY IF EXISTS "Instructors can view all students" ON students;
        DROP POLICY IF EXISTS "Instructors can view all requests" ON account_link_requests;
        
        -- Drop the constraint if it exists
        ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_role_check;
        
        -- Use a temporary column for the conversion
        ALTER TABLE profiles ADD COLUMN role_enum user_role;
        UPDATE profiles SET role_enum = role::user_role;
        
        -- Drop the old column and rename the new one
        ALTER TABLE profiles DROP COLUMN role;
        ALTER TABLE profiles RENAME COLUMN role_enum TO role;
        
        -- Set not null constraint and default
        ALTER TABLE profiles ALTER COLUMN role SET NOT NULL;
        ALTER TABLE profiles ALTER COLUMN role SET DEFAULT 'student'::user_role;
        
        -- Re-create the policies we dropped
        CREATE POLICY "Users can read own profile" ON profiles FOR SELECT TO authenticated USING (auth.uid() = id);
        CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE TO authenticated USING (auth.uid() = id);
        CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT TO authenticated WITH CHECK (auth.uid() = id);
        CREATE POLICY "Service role can manage all profiles" ON profiles TO service_role USING (true);
        CREATE POLICY "Instructors can view all students" ON students FOR SELECT TO authenticated USING ((SELECT role FROM profiles WHERE profiles.id = auth.uid()) = 'instructor'::user_role OR (SELECT role FROM profiles WHERE profiles.id = auth.uid()) = 'admin'::user_role);
        CREATE POLICY "Instructors can view all requests" ON account_link_requests FOR SELECT TO authenticated USING ((SELECT role FROM profiles WHERE profiles.id = auth.uid()) = 'instructor'::user_role OR (SELECT role FROM profiles WHERE profiles.id = auth.uid()) = 'admin'::user_role);
    END IF;
    
    -- Add role column if it doesn't exist at all
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'role') THEN
        ALTER TABLE profiles ADD COLUMN role user_role DEFAULT 'student'::user_role NOT NULL;
    END IF;
END $$;

-- 3. Add missing columns to syllabus table
DO $$ 
BEGIN
    -- Add belt_level column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'syllabus' AND column_name = 'belt_level') THEN
        ALTER TABLE syllabus ADD COLUMN belt_level TEXT NOT NULL DEFAULT 'white';
    END IF;
    
    -- Add category column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'syllabus' AND column_name = 'category') THEN
        ALTER TABLE syllabus ADD COLUMN category TEXT NOT NULL DEFAULT 'Techniques';
    END IF;
    
    -- Add skill_name column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'syllabus' AND column_name = 'skill_name') THEN
        ALTER TABLE syllabus ADD COLUMN skill_name TEXT NOT NULL DEFAULT 'Basic Skill';
    END IF;
    
    -- Add description column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'syllabus' AND column_name = 'description') THEN
        ALTER TABLE syllabus ADD COLUMN description TEXT;
    END IF;
    
    -- Add requirements column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'syllabus' AND column_name = 'requirements') THEN
        ALTER TABLE syllabus ADD COLUMN requirements TEXT;
    END IF;
    
    -- Add difficulty_level column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'syllabus' AND column_name = 'difficulty_level') THEN
        ALTER TABLE syllabus ADD COLUMN difficulty_level INTEGER CHECK (difficulty_level >= 1 AND difficulty_level <= 5) DEFAULT 1;
    END IF;
    
    -- Add is_required column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'syllabus' AND column_name = 'is_required') THEN
        ALTER TABLE syllabus ADD COLUMN is_required BOOLEAN DEFAULT false;
    END IF;
    
    -- Add display_order column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'syllabus' AND column_name = 'display_order') THEN
        ALTER TABLE syllabus ADD COLUMN display_order INTEGER DEFAULT 0;
    END IF;
END $$;

-- 4. Fix account_link_requests table structure
DO $$ 
BEGIN
    -- Add missing columns to account_link_requests if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'account_link_requests' AND column_name = 'requested_by') THEN
        ALTER TABLE account_link_requests ADD COLUMN requested_by UUID REFERENCES profiles(id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'account_link_requests' AND column_name = 'reviewed_by') THEN
        ALTER TABLE account_link_requests ADD COLUMN reviewed_by UUID REFERENCES profiles(id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'account_link_requests' AND column_name = 'requested_at') THEN
        ALTER TABLE account_link_requests ADD COLUMN requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'account_link_requests' AND column_name = 'reviewed_at') THEN
        ALTER TABLE account_link_requests ADD COLUMN reviewed_at TIMESTAMP WITH TIME ZONE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'account_link_requests' AND column_name = 'relationship_type') THEN
        ALTER TABLE account_link_requests ADD COLUMN relationship_type TEXT CHECK (relationship_type IN ('self', 'guardian')) DEFAULT 'guardian';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'account_link_requests' AND column_name = 'request_message') THEN
        ALTER TABLE account_link_requests ADD COLUMN request_message TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'account_link_requests' AND column_name = 'status') THEN
        ALTER TABLE account_link_requests ADD COLUMN status TEXT CHECK (status IN ('pending', 'approved', 'rejected')) DEFAULT 'pending';
    END IF;
END $$;

-- 5. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_syllabus_belt_category ON syllabus(belt_level, category);
CREATE INDEX IF NOT EXISTS idx_account_link_requests_status ON account_link_requests(status);
CREATE INDEX IF NOT EXISTS idx_account_link_requests_requested_by ON account_link_requests(requested_by);
