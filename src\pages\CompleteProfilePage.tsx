import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { ProfileForm } from '../components/ProfileForm';
import { CheckCircle, UserPlus, ArrowRight, AlertCircle, RefreshCw } from 'lucide-react';
import { createInitialProfile, fetchUserProfile } from '../services/authService';
import { PageCenter } from '../components/PageLayout';

export const CompleteProfilePage: React.FC = () => {
  const { state, dispatch } = useAuth();
  const { user, loading } = state;
  const navigate = useNavigate();
  const [profileCompleted, setProfileCompleted] = useState(false);
  const [countdown, setCountdown] = useState(5);
  const [error, setError] = useState<string | null>(null);
  const [isRetrying, setIsRetrying] = useState(false);

  // Protect the route
  useEffect(() => {
    if (!loading && !user) {
      navigate('/signin', { replace: true });
    }
  }, [user, loading, navigate]);

  // Countdown and redirect after profile completion
  useEffect(() => {
    if (profileCompleted) {
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            navigate('/dashboard');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [profileCompleted, navigate]);

  const handleProfileSaved = () => {
    setProfileCompleted(true);
  };
  
  const handleRetryProfileCreation = async () => {
    if (!user) return;
    
    setIsRetrying(true);
    setError(null);
    
    try {
      // Try to create a basic profile with empty values that user can update
      const { error: createError } = await createInitialProfile(
        user.id, 
        'New', // Placeholder first name
        'User', // Placeholder last name
        'student' // Default role
      );
      
      if (createError) {
        setError('Could not create your profile. Please try again or contact support.');
      } else {
        // Fetch the newly created profile
        const { data: profileData } = await fetchUserProfile(user.id);
        if (profileData) {
          dispatch({ type: 'SET_PROFILE', payload: profileData });
          setError(null);
        }
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
      console.error('Error in profile recovery:', err);
    } finally {
      setIsRetrying(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-pulse flex space-x-4">
          <div className="rounded-full bg-gray-200 h-10 w-10"></div>
          <div className="flex-1 space-y-4 py-1">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (profileCompleted) {
    return (
      <PageCenter maxWidth="max-w-xl" className="text-center">
        <div className="flex justify-center mb-4">
          <CheckCircle className="w-16 h-16 text-green-500" />
        </div>
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Profile Completed!</h2>
        <p className="text-gray-600 mb-6">
          Thank you for completing your profile. Your account is now ready to use.
        </p>
        <div className="bg-green-50 text-green-700 px-4 py-2 rounded-lg mb-6">
          You will be redirected to your dashboard in {countdown} seconds.
        </div>
        <button
          onClick={() => navigate('/dashboard')}
          className="inline-flex items-center px-6 py-2.5 bg-indigo-600 text-white font-medium rounded-lg shadow-md hover:bg-indigo-700"
        >
          Go to Dashboard <ArrowRight className="ml-2 w-4 h-4" />
        </button>
      </PageCenter>
    );
  }

  return (
    <PageCenter maxWidth="max-w-2xl" className="p-6">
      <div className="flex items-center justify-center mb-6">
        <UserPlus className="w-10 h-10 text-indigo-600 mr-2" />
        <h2 className="text-2xl font-bold text-gray-800">Complete Your Profile</h2>
      </div>
      
      {error && (
        <div className="p-4 mb-6 bg-yellow-50 border-l-4 border-yellow-500 text-yellow-700">
          <p className="flex items-center">
            <AlertCircle className="w-5 h-5 mr-2" />
            {error}
          </p>
          <div className="mt-2">
            <button
              onClick={handleRetryProfileCreation}
              className="flex items-center text-yellow-800 font-medium hover:text-yellow-900"
              disabled={isRetrying}
            >
              <RefreshCw className={`w-4 h-4 mr-1 ${isRetrying ? 'animate-spin' : ''}`} />
              {isRetrying ? 'Retrying...' : 'Retry profile creation'}
            </button>
          </div>
        </div>
      )}
      
      <ProfileForm onSaved={handleProfileSaved} />
    </PageCenter>
  );
};
