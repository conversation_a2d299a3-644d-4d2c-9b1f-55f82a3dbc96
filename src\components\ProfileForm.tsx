import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { upsertProfile } from '../services/authService';
import { useAuth } from '../contexts/AuthContext';
import { Save, Loader2, CheckCircle, AlertCircle, UserPlus, Heart, Award } from 'lucide-react';
import { MedicalConditionsSelector } from './MedicalConditionsSelector';
import { supabase } from '../services/supabaseClient';

// Create schema based on mode and user role
const createSchema = (mode: 'complete' | 'full', userRole?: string) => {
  const isStudent = userRole === 'student' || !userRole; // Default to student if role not specified
  
  return z.object({
    first_name: z.string().min(1, 'First name is required'),
    last_name: z.string().min(1, 'Last name is required'),
    phone: z.string().min(1, 'Phone number is required'), // Always required
    date_of_birth: z.string().min(1, 'Date of birth is required'),
    address: z.object({
      street: z.string().min(1, 'Street address is required'),
      city: z.string().min(1, 'City is required'),
      state: z.string().min(1, 'State/Province is required'),
      postal_code: z.string().min(1, 'Postal code is required'),
      country: z.string().min(1, 'Country is required'),
    }),
    // Emergency contact only required for students
    emergency_contact: isStudent ? z.object({
      name: z.string().min(1, 'Emergency contact name is required'),
      relationship: z.string().min(1, 'Relationship is required'),
      phone: z.string().min(1, 'Emergency contact phone is required'),
      email: z.string().email('Valid email is required').optional(),
    }) : z.object({
      name: z.string().optional(),
      relationship: z.string().optional(),
      phone: z.string().optional(),
      email: z.string().email().optional().or(z.literal('')),
    }).optional(),
    medical_conditions: z.array(z.any()).optional(),
    // Consent only required for students in complete mode
    consent_social_media: (mode === 'complete' && isStudent) ? z.boolean().refine(val => val === true, 'Social media consent is required') : z.boolean(),
    consent_assumption_risk: (mode === 'complete' && isStudent) ? z.boolean().refine(val => val === true, 'Assumption of risk consent is required') : z.boolean(),
    // Student-specific fields - only validated in full mode
    belt_color: mode === 'full' ? z.string().optional() : z.string().optional(),
    insurance_expiry: mode === 'full' ? z.string().optional() : z.string().optional(),
    licence_number: mode === 'full' ? z.string().optional() : z.string().optional(),
  });
};

type ProfileFormData = z.infer<ReturnType<typeof createSchema>>;

interface SelectedCondition {
  condition_id: string;
  condition_name: string;
  notes?: string;
  severity?: string;
  medication?: string;
}

interface ProfileFormProps {
  onSaved?: () => void;
  mode?: 'complete' | 'full'; // 'complete' for new signups, 'full' for existing users
}

export const ProfileForm: React.FC<ProfileFormProps> = ({ onSaved, mode = 'full' }) => {
  const { state, dispatch } = useAuth();
  const { user, profile } = state;
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [successMessage, setSuccessMessage] = React.useState('');
  const [errorMessage, setErrorMessage] = React.useState('');
  const [medicalConditions, setMedicalConditions] = React.useState<SelectedCondition[]>([]);
  const [hasChangedMedical, setHasChangedMedical] = React.useState(false);
  const [studentData, setStudentData] = React.useState<any>(null);  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<ProfileFormData>({
    resolver: zodResolver(createSchema(mode, profile?.role)),
    defaultValues: {
      first_name: profile?.first_name || '',
      last_name: profile?.last_name || '',
      phone: profile?.phone || '',
      date_of_birth: profile?.date_of_birth || '',
      address: profile?.address || {
        street: '',
        city: '',
        state: '',
        postal_code: '',
        country: '',
      },
      emergency_contact: {
        name: '',
        relationship: '',
        phone: '',
        email: '',
      },
      medical_conditions: [],
      consent_social_media: false,
      consent_assumption_risk: false,
      belt_color: 'white',
      insurance_expiry: '',
      licence_number: '',
    },
  });  React.useEffect(() => {
    if (profile) {
      setValue('first_name', profile.first_name || '');
      setValue('last_name', profile.last_name || '');
      setValue('phone', profile.phone || '');
      setValue('date_of_birth', profile.date_of_birth || '');
      setValue('address', profile.address || {
        street: '',
        city: '',
        state: '',
        postal_code: '',
        country: '',
      });
      
      // If this is a student profile, load their student record
      if (profile.role === 'student' && user) {
        const fetchStudentData = async () => {
          const { data, error } = await supabase
            .from('students')
            .select('*')
            .eq('user_id', user.id)
            .single();
            
          if (data && !error) {
            setStudentData(data);
            // Set student-specific fields
            setValue('belt_color', data.belt_color || 'white');
            setValue('insurance_expiry', data.insurance_expiry || '');
            setValue('licence_number', data.licence_number || '');
            setValue('emergency_contact', data.emergency_contact || {
              name: '',
              relationship: '',
              phone: '',
              email: '',
            });
            setValue('consent_social_media', data.consent_social_media || false);
            setValue('consent_assumption_risk', data.consent_assumption_risk || false);
              // If medical conditions exist, set them with proper data structure
            if (data.medical_conditions && Array.isArray(data.medical_conditions)) {
              const conditions = data.medical_conditions.map((condition: any, index: number) => {
                // Handle different data structures
                if (typeof condition === 'string') {
                  // If it's a string, convert to object
                  return {
                    condition_id: `existing_${index}`,
                    condition_name: condition,
                    notes: '',
                    severity: 'mild',
                    medication: ''
                  };
                } else if (condition && typeof condition === 'object') {
                  // If it's already an object, ensure it has all required fields
                  return {
                    condition_id: condition.condition_id || `existing_${index}`,
                    condition_name: condition.condition_name || condition.name || 'Unknown Condition',
                    notes: condition.notes || '',
                    severity: condition.severity || 'mild',
                    medication: condition.medication || ''
                  };
                } else {
                  // Fallback for unexpected data
                  return {
                    condition_id: `existing_${index}`,
                    condition_name: 'Unknown Condition',
                    notes: '',
                    severity: 'mild',
                    medication: ''
                  };
                }
              });
              
              setMedicalConditions(conditions);
              setValue('medical_conditions', conditions);
            }
          }
        };
        
        fetchStudentData();
      }
    } else if (user) {
      // If we have a user but no profile, set defaults
      setValue('first_name', '');
      setValue('last_name', '');
    }
  }, [profile, user, setValue]);  const onSubmit = async (data: ProfileFormData) => {
    if (!user) return;
    
    setIsSubmitting(true);
    setSuccessMessage('');
    setErrorMessage('');
    
    try {      // First, update the profile
      const { error: profileError } = await upsertProfile(user.id, {
        first_name: data.first_name,
        last_name: data.last_name,
        phone: data.phone,
        date_of_birth: data.date_of_birth,
        address: data.address,
        role: profile?.role || 'student',
      });
      
      if (profileError) {
        console.error('Error updating profile:', profileError);
        setErrorMessage('Failed to update profile information');
        setIsSubmitting(false);
        return;
      }      // If this is a student, create or update the student record
      if (profile?.role === 'student') {
        const studentUpdateData = {
          user_id: user.id,
          first_name: data.first_name,
          last_name: data.last_name,
          date_of_birth: data.date_of_birth!,
          address: data.address,
          phone: data.phone,
          emergency_contact: data.emergency_contact,
          medical_conditions: medicalConditions,
          consent_social_media: data.consent_social_media || false,
          consent_assumption_risk: data.consent_assumption_risk || false,
          active: true,
          // Only include martial arts fields if in full mode or if they already exist
          ...(mode === 'full' && {
            belt_color: data.belt_color || 'white',
            insurance_expiry: data.insurance_expiry,
            licence_number: data.licence_number,
          }),
          // If in complete mode but student already exists, preserve existing martial arts data
          ...(mode === 'complete' && studentData && {
            belt_color: studentData.belt_color,
            insurance_expiry: studentData.insurance_expiry,
            licence_number: studentData.licence_number,
          }),
          // If in complete mode and no existing student, set belt_color to white
          ...(mode === 'complete' && !studentData && {
            belt_color: 'white',
          }),
        };

        if (studentData) {
          // Update existing student record
          const { error: studentError } = await supabase
            .from('students')
            .update({
              ...studentUpdateData,
              medical_conditions_updated: hasChangedMedical ? new Date().toISOString() : undefined
            })
            .eq('user_id', user.id);
            
          if (studentError) {
            console.error('Error updating student record:', studentError);
            setErrorMessage('Profile updated but failed to update student information');
          }
        } else {
          // Create new student record
          const { error: studentError } = await supabase
            .from('students')
            .insert([studentUpdateData]);
            
          if (studentError) {
            console.error('Error creating student record:', studentError);
            setErrorMessage('Profile updated but failed to create student record');
          }
        }

        // If medical conditions were changed, notify instructors
        if (hasChangedMedical) {
          await supabase.from('notifications').insert({
            user_id: user.id,
            type: 'medical_update',
            content: `${data.first_name} ${data.last_name} has updated their medical conditions.`,
            read: false
          });
        }
      }

      // Update the profile in context
      dispatch({ 
        type: 'SET_PROFILE', 
        payload: { 
          ...(profile || { id: user.id, role: 'student' }),
          first_name: data.first_name,
          last_name: data.last_name,
          phone: data.phone,
          date_of_birth: data.date_of_birth,
          address: data.address,
          id: user.id,
          role: profile?.role || 'student',
          updated_at: new Date().toISOString()
        } 
      });
      
      setSuccessMessage('Profile updated successfully');
      if (onSaved) {
        onSaved();
      }
    } catch (error: any) {
      console.error('Profile update error:', error);
      setErrorMessage(error?.message || 'An error occurred while updating your profile');
    } finally {
      setIsSubmitting(false);
    }
  };  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 max-w-5xl mx-auto">      {/* Basic Information */}
      <div className="p-3 sm:p-4 bg-white rounded-lg border border-gray-200">
        <h3 className="text-base sm:text-lg font-medium text-gray-800 mb-3 sm:mb-4">Basic Information</h3>
        <div className="grid grid-cols-1 gap-3 sm:gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div>
          <label className="block mb-1 font-medium text-gray-700">First Name *</label>
          <input
            type="text"
            {...register('first_name')}
            className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
          />
          {errors.first_name && <p className="text-red-600 text-sm mt-1">{errors.first_name.message}</p>}
        </div>

        <div>
          <label className="block mb-1 font-medium text-gray-700">Last Name *</label>
          <input
            type="text"
            {...register('last_name')}
            className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
          />
          {errors.last_name && <p className="text-red-600 text-sm mt-1">{errors.last_name.message}</p>}
        </div>        <div>
          <label className="block mb-1 font-medium text-gray-700">Phone *</label>
          <input
            type="tel"
            {...register('phone')}
            className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
          />
          {errors.phone && <p className="text-red-600 text-sm mt-1">{errors.phone.message}</p>}
        </div>

        <div>
          <label className="block mb-1 font-medium text-gray-700">Date of Birth *</label>
          <input
            type="date"
            {...register('date_of_birth')}
            className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
          />
          {errors.date_of_birth && <p className="text-red-600 text-sm mt-1">{errors.date_of_birth.message}</p>}        </div>
      </div>
      </div>      {/* Address Section */}
      <div className="p-3 sm:p-4 bg-gray-50 rounded-lg border border-gray-200">
        <h3 className="text-base sm:text-lg font-medium text-gray-800 mb-3">Address *</h3>
        <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3"><div className="lg:col-span-3">
            <label className="block mb-1 font-medium text-gray-700">Street *</label>
            <input
              type="text"
              {...register('address.street')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
            />
            {errors.address?.street && <p className="text-red-600 text-sm mt-1">{errors.address.street.message}</p>}
          </div>

          <div>
            <label className="block mb-1 font-medium text-gray-700">City *</label>
            <input
              type="text"
              {...register('address.city')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
            />
            {errors.address?.city && <p className="text-red-600 text-sm mt-1">{errors.address.city.message}</p>}
          </div>

          <div>
            <label className="block mb-1 font-medium text-gray-700">State/Province *</label>
            <input
              type="text"
              {...register('address.state')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
            />
            {errors.address?.state && <p className="text-red-600 text-sm mt-1">{errors.address.state.message}</p>}
          </div>

          <div>
            <label className="block mb-1 font-medium text-gray-700">Postal Code *</label>
            <input
              type="text"
              {...register('address.postal_code')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
            />
            {errors.address?.postal_code && <p className="text-red-600 text-sm mt-1">{errors.address.postal_code.message}</p>}
          </div>

          <div>
            <label className="block mb-1 font-medium text-gray-700">Country *</label>
            <input
              type="text"
              {...register('address.country')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
            />
            {errors.address?.country && <p className="text-red-600 text-sm mt-1">{errors.address.country.message}</p>}          </div>
        </div>
      </div>      {/* Emergency Contact Section - Only for students */}
      {(profile?.role === 'student' || (mode === 'complete' && !profile)) && (
        <div className="p-3 sm:p-4 bg-red-50 rounded-lg border border-red-200">
          <h3 className="text-base sm:text-lg font-medium text-gray-800 mb-3 flex items-center">
            <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5 text-red-500 mr-2" />
            Emergency Contact *
          </h3>
        <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4">
          <div>
            <label className="block mb-1 font-medium text-gray-700">Contact Name *</label>
            <input
              type="text"
              {...register('emergency_contact.name')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
            />
            {errors.emergency_contact?.name && <p className="text-red-600 text-sm mt-1">{errors.emergency_contact.name.message}</p>}
          </div>

          <div>
            <label className="block mb-1 font-medium text-gray-700">Relationship *</label>
            <select
              {...register('emergency_contact.relationship')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
            >
              <option value="">Select relationship</option>
              <option value="parent">Parent</option>
              <option value="guardian">Guardian</option>
              <option value="spouse">Spouse</option>
              <option value="sibling">Sibling</option>
              <option value="friend">Friend</option>
              <option value="other">Other</option>
            </select>
            {errors.emergency_contact?.relationship && <p className="text-red-600 text-sm mt-1">{errors.emergency_contact.relationship.message}</p>}
          </div>

          <div>
            <label className="block mb-1 font-medium text-gray-700">Phone *</label>
            <input
              type="tel"
              {...register('emergency_contact.phone')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
            />
            {errors.emergency_contact?.phone && <p className="text-red-600 text-sm mt-1">{errors.emergency_contact.phone.message}</p>}
          </div>

          <div>
            <label className="block mb-1 font-medium text-gray-700">Email</label>
            <input
              type="email"
              {...register('emergency_contact.email')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
            />
            {errors.emergency_contact?.email && <p className="text-red-600 text-sm mt-1">{errors.emergency_contact.email.message}</p>}          </div>
        </div>
      </div>
      )}

      {/* Student-specific fields - only shown in full mode (these are set by instructors) */}
      {mode === 'full' && profile?.role === 'student' && (
        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h3 className="text-md font-medium text-gray-800 mb-3 flex items-center">
            <Award className="h-5 w-5 text-blue-500 mr-2" />
            Martial Arts Information
            <span className="ml-2 text-sm text-gray-500">(Set by Instructor)</span>
          </h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div>
              <label className="block mb-1 font-medium text-gray-700">Current Belt Color *</label>
              <select
                {...register('belt_color')}
                className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              >
                <option value="white">White Belt</option>
                <option value="yellow">Yellow Belt</option>
                <option value="orange">Orange Belt</option>
                <option value="green">Green Belt</option>
                <option value="blue">Blue Belt</option>
                <option value="purple">Purple Belt</option>
                <option value="brown">Brown Belt</option>
                <option value="black">Black Belt</option>
              </select>
              {errors.belt_color && <p className="text-red-600 text-sm mt-1">{errors.belt_color.message}</p>}
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700">Insurance Expiry</label>
              <input
                type="date"
                {...register('insurance_expiry')}
                className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              />
              {errors.insurance_expiry && <p className="text-red-600 text-sm mt-1">{errors.insurance_expiry.message}</p>}
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700">Licence Number</label>
              <input
                type="text"
                {...register('licence_number')}
                placeholder="e.g., MA12345"
                className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              />
              {errors.licence_number && <p className="text-red-600 text-sm mt-1">{errors.licence_number.message}</p>}
            </div>
          </div>        </div>
      )}      {/* Medical Conditions Section - For students or complete mode */}
      {(profile?.role === 'student' || mode === 'complete') && (
        <div className="p-3 sm:p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <div className="flex items-center mb-3">
            <Heart className="h-4 w-4 sm:h-5 sm:w-5 text-red-500 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-800">Medical Information</h3>
          </div>
            {hasChangedMedical && (
            <div className="bg-amber-100 border-l-4 border-amber-500 text-amber-700 p-3 mb-3">
              <p className="text-sm">
                <strong>Note:</strong> Any changes to your medical information will be flagged for review by an instructor.
              </p>
            </div>
          )}
          
          <div className="mt-3">
            <MedicalConditionsSelector 
              selectedConditions={medicalConditions}
              onChange={(newConditions) => {
                setMedicalConditions(newConditions);
                setValue('medical_conditions', newConditions);
                setHasChangedMedical(true);
              }}
            />
          </div>
        </div>
      )}      {/* Consent Section - Only for students */}
      {(profile?.role === 'student' || (mode === 'complete' && !profile)) && (
        <div className="p-3 sm:p-4 bg-green-50 rounded-lg border border-green-200">
          <h3 className="text-base sm:text-lg font-medium text-gray-800 mb-3 flex items-center">
            <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-500 mr-2" />
            Consent & Agreements *
          </h3>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <input
                type="checkbox"
                {...register('consent_social_media')}
                className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />            
              <div className="flex-1">
                <label className="text-xs sm:text-sm text-gray-700">
                  <strong>Social Media Consent:</strong> I consent to photographs and videos being taken during classes and events, and for these to be used in the club's marketing materials and social media.
                  {mode === 'complete' && <span className="text-red-500 ml-1">*</span>}
                </label>
                {errors.consent_social_media && <p className="text-red-600 text-xs mt-1">{errors.consent_social_media.message}</p>}
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <input
                type="checkbox"
                {...register('consent_assumption_risk')}
                className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <div className="flex-1">
                <label className="text-xs sm:text-sm text-gray-700">
                  <strong>Assumption of Risk:</strong> I understand that martial arts training involves physical contact and carries inherent risks of injury. I assume all risks and agree to participate at my own risk.
                  {mode === 'complete' && <span className="text-red-500 ml-1">*</span>}
                </label>
                {errors.consent_assumption_risk && <p className="text-red-600 text-xs mt-1">{errors.consent_assumption_risk.message}</p>}
              </div>
            </div>
          </div>
        </div>
      )}<div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mt-4 gap-4">
        <div className="flex-1">
          {successMessage && (
            <p className="text-green-600 flex items-center text-sm">
              <CheckCircle className="w-4 h-4 mr-1" /> {successMessage}
            </p>
          )}
          {errorMessage && (
            <p className="text-red-600 flex items-center text-sm">
              <AlertCircle className="w-4 h-4 mr-1" /> {errorMessage}
            </p>
          )}
        </div>
        
        <button
          type="submit"
          disabled={isSubmitting}
          className={`inline-flex items-center px-6 py-2.5 ${!profile 
            ? 'bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600' 
            : 'bg-gradient-to-r from-indigo-600 to-blue-500 hover:from-indigo-700 hover:to-blue-600'
          } text-white font-bold rounded-lg shadow transition`}        >
          {isSubmitting ? (
            <>
              <Loader2 className="animate-spin w-5 h-5 mr-2" />
              {mode === 'complete' ? 'Completing Setup...' : (profile ? 'Saving...' : 'Creating Profile...')}
            </>
          ) : (
            <>
              {mode === 'complete' ? (
                <>
                  <UserPlus className="w-5 h-5 mr-2" />
                  Complete Sign Up
                </>
              ) : profile ? (
                <>
                  <Save className="w-5 h-5 mr-2" />
                  Update Profile
                </>
              ) : (
                <>
                  <UserPlus className="w-5 h-5 mr-2" />
                  Create Profile
                </>
              )}
            </>
          )}        </button>
      </div>
    </form>
  );
};
