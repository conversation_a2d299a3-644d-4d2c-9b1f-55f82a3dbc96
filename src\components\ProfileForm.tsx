import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { upsertProfile } from '../services/authService';
import { useAuth } from '../contexts/AuthContext';
import { Save, Loader2, CheckCircle, AlertCircle, UserPlus } from 'lucide-react';

const schema = z.object({
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  phone: z.string().optional(),
  date_of_birth: z.string().optional(),
  address: z.object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    postal_code: z.string().optional(),
    country: z.string().optional(),
  }).optional(),
});

type ProfileFormData = z.infer<typeof schema>;

interface ProfileFormProps {
  onSaved?: () => void;
}

export const ProfileForm: React.FC<ProfileFormProps> = ({ onSaved }) => {
  const { state, dispatch } = useAuth();
  const { user, profile } = state;
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [successMessage, setSuccessMessage] = React.useState('');
  const [errorMessage, setErrorMessage] = React.useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm<ProfileFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      first_name: profile?.first_name || '',
      last_name: profile?.last_name || '',
      phone: profile?.phone || '',
      date_of_birth: profile?.date_of_birth || '',
      address: profile?.address || {
        street: '',
        city: '',
        state: '',
        postal_code: '',
        country: '',
      },
    },
  });
  React.useEffect(() => {
    if (profile) {
      setValue('first_name', profile.first_name || '');
      setValue('last_name', profile.last_name || '');
      setValue('phone', profile.phone || '');
      setValue('date_of_birth', profile.date_of_birth || '');
      setValue('address', profile.address || {
        street: '',
        city: '',
        state: '',
        postal_code: '',
        country: '',
      });
    } else if (user) {
      // If we have a user but no profile, set defaults
      setValue('first_name', '');
      setValue('last_name', '');
    }
  }, [profile, user, setValue]);
  const onSubmit = async (data: ProfileFormData) => {
    if (!user) return;
    
    setIsSubmitting(true);
    setSuccessMessage('');
    setErrorMessage('');
    
    try {
      // First, try updating with the most reliable method
      const { error } = await upsertProfile(user.id, {
        ...data,
        role: profile?.role || 'student', // Preserve role if it exists
      });
      
      if (error) {
        console.error('Error updating profile:', error);
        // Safely handle error object which might be of different types
        const errorMessage = typeof error === 'object' && error !== null && 'message' in error
          ? String(error.message)
          : 'An error occurred while updating your profile';
          
        setErrorMessage(errorMessage);
        setIsSubmitting(false);
        return;
      }
        // Update the profile in context (but we'll refresh from server in the onSaved callback)
      dispatch({ 
        type: 'SET_PROFILE', 
        payload: { 
          // Start with the existing profile if available
          ...(profile || { id: user.id, role: 'student' }),
          // Override with new data
          ...data,
          // Always ensure these critical fields are present
          id: user.id,
          role: profile?.role || 'student',
          updated_at: new Date().toISOString()
        } 
      });
      
      setSuccessMessage('Profile updated successfully');
      if (onSaved) {
        // Call the onSaved callback which will refresh profile from server
        onSaved();
      }
    } catch (error: any) {
      console.error('Profile update error:', error);
      setErrorMessage(error?.message || 'An error occurred while updating your profile');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div>
          <label className="block mb-1 font-medium text-gray-700">First Name</label>
          <input
            type="text"
            {...register('first_name')}
            className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
          />
          {errors.first_name && <p className="text-red-600 text-sm mt-1">{errors.first_name.message}</p>}
        </div>

        <div>
          <label className="block mb-1 font-medium text-gray-700">Last Name</label>
          <input
            type="text"
            {...register('last_name')}
            className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
          />
          {errors.last_name && <p className="text-red-600 text-sm mt-1">{errors.last_name.message}</p>}
        </div>

        <div>
          <label className="block mb-1 font-medium text-gray-700">Phone</label>
          <input
            type="tel"
            {...register('phone')}
            className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
          />
          {errors.phone && <p className="text-red-600 text-sm mt-1">{errors.phone.message}</p>}
        </div>

        <div>
          <label className="block mb-1 font-medium text-gray-700">Date of Birth</label>
          <input
            type="date"
            {...register('date_of_birth')}
            className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
          />
          {errors.date_of_birth && <p className="text-red-600 text-sm mt-1">{errors.date_of_birth.message}</p>}
        </div>
      </div>

      <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
        <h3 className="text-md font-medium text-gray-800 mb-3">Address</h3>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="md:col-span-2">
            <label className="block mb-1 font-medium text-gray-700">Street</label>
            <input
              type="text"
              {...register('address.street')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
            />
          </div>

          <div>
            <label className="block mb-1 font-medium text-gray-700">City</label>
            <input
              type="text"
              {...register('address.city')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
            />
          </div>

          <div>
            <label className="block mb-1 font-medium text-gray-700">State/Province</label>
            <input
              type="text"
              {...register('address.state')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
            />
          </div>

          <div>
            <label className="block mb-1 font-medium text-gray-700">Postal Code</label>
            <input
              type="text"
              {...register('address.postal_code')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
            />
          </div>

          <div>
            <label className="block mb-1 font-medium text-gray-700">Country</label>
            <input
              type="text"
              {...register('address.country')}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
            />
          </div>
        </div>
      </div>      <div className="flex justify-between items-center mt-6">
        <div>
          {successMessage && (
            <p className="text-green-600 flex items-center">
              <CheckCircle className="w-5 h-5 mr-2" /> {successMessage}
            </p>
          )}
          {errorMessage && (
            <p className="text-red-600 flex items-center">
              <AlertCircle className="w-5 h-5 mr-2" /> {errorMessage}
            </p>
          )}
        </div>
        
        <button
          type="submit"
          disabled={isSubmitting}
          className={`inline-flex items-center px-6 py-2.5 ${!profile 
            ? 'bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600' 
            : 'bg-gradient-to-r from-indigo-600 to-blue-500 hover:from-indigo-700 hover:to-blue-600'
          } text-white font-bold rounded-lg shadow transition`}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="animate-spin w-5 h-5 mr-2" />
              {profile ? 'Saving...' : 'Creating Profile...'}
            </>
          ) : (
            <>
              {profile ? (
                <>
                  <Save className="w-5 h-5 mr-2" />
                  Update Profile
                </>
              ) : (
                <>
                  <UserPlus className="w-5 h-5 mr-2" />
                  Create Profile
                </>
              )}
            </>
          )}
        </button>
      </div>
    </form>
  );
};
