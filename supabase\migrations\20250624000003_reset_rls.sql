-- Complete RLS reset to fix recursion issues
-- This migration will disable <PERSON><PERSON> temporarily to get the app working

-- Disable <PERSON><PERSON> on all tables temporarily
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE students DISABLE ROW LEVEL SECURITY;
ALTER TABLE sessions DISABLE ROW LEVEL SECURITY;
ALTER TABLE attendance DISABLE ROW LEVEL SECURITY;
ALTER TABLE syllabus DISABLE ROW LEVEL SECURITY;
ALTER TABLE gradings DISABLE ROW LEVEL SECURITY;
ALTER TABLE exercises DISABLE ROW LEVEL SECURITY;
ALTER TABLE fitness_records DISABLE ROW LEVEL SECURITY;
ALTER TABLE account_link_requests DISABLE ROW LEVEL SECURITY;

-- Drop ALL existing policies to ensure clean slate
-- Profiles policies
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
DROP POLICY IF EXISTS "Instructors and admins can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON profiles;
DROP POLICY IF EXISTS "Enable update for users based on email" ON profiles;

-- Students policies
DROP POLICY IF EXISTS "Instructors and admins can manage students" ON students;
DROP POLICY IF EXISTS "Users can view linked students" ON students;
DROP POLICY IF EXISTS "Guardians can view their students" ON students;
DROP POLICY IF EXISTS "Authenticated users can manage students" ON students;

-- Sessions policies
DROP POLICY IF EXISTS "Instructors and admins can manage sessions" ON sessions;
DROP POLICY IF EXISTS "All authenticated users can view sessions" ON sessions;
DROP POLICY IF EXISTS "Authenticated users can manage sessions" ON sessions;

-- Attendance policies
DROP POLICY IF EXISTS "Instructors and admins can manage attendance" ON attendance;
DROP POLICY IF EXISTS "Users can view attendance for linked students" ON attendance;
DROP POLICY IF EXISTS "Authenticated users can manage attendance" ON attendance;

-- Syllabus policies
DROP POLICY IF EXISTS "All authenticated users can view syllabus" ON syllabus;
DROP POLICY IF EXISTS "Admins can manage syllabus" ON syllabus;
DROP POLICY IF EXISTS "Authenticated users can access syllabus" ON syllabus;

-- Gradings policies
DROP POLICY IF EXISTS "Instructors and admins can manage gradings" ON gradings;
DROP POLICY IF EXISTS "Users can view gradings for linked students" ON gradings;
DROP POLICY IF EXISTS "Authenticated users can manage gradings" ON gradings;

-- Exercises policies
DROP POLICY IF EXISTS "Instructors and admins can manage exercises" ON exercises;
DROP POLICY IF EXISTS "All authenticated users can view exercises" ON exercises;
DROP POLICY IF EXISTS "Authenticated users can manage exercises" ON exercises;

-- Fitness records policies
DROP POLICY IF EXISTS "Instructors and admins can manage fitness records" ON fitness_records;
DROP POLICY IF EXISTS "Users can view fitness records for linked students" ON fitness_records;
DROP POLICY IF EXISTS "Authenticated users can manage fitness records" ON fitness_records;

-- Account link requests policies
DROP POLICY IF EXISTS "Users can manage their own requests" ON account_link_requests;
DROP POLICY IF EXISTS "Instructors and admins can manage all requests" ON account_link_requests;
DROP POLICY IF EXISTS "Authenticated users can manage account link requests" ON account_link_requests;

-- Drop any functions that might cause issues
DROP FUNCTION IF EXISTS auth.user_role();

-- Note: RLS is now disabled on all tables
-- This allows the app to work without policy restrictions
-- We can re-enable RLS with proper policies later once the app is stable
