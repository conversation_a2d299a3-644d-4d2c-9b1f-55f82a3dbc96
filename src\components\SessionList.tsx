import React, { useState, useEffect } from 'react';
import { Calendar, Clock, Users, Plus, Filter, Eye, Edit, Trash2, CheckSquare } from 'lucide-react';
import { sessionService } from '../services';
import type { Session, SessionFilters } from '../types';
import { useAuth } from '../contexts/AuthContext';

interface SessionListProps {
  onSelectSession?: (session: Session) => void;
  onEditSession?: (session: Session) => void;
  onCreateSession?: () => void;
  onTrackAttendance?: (session: Session) => void;
  showActions?: boolean;
  filters?: SessionFilters;
}

export const SessionList: React.FC<SessionListProps> = ({
  onSelectSession,
  onEditSession,
  onCreateSession,
  onTrackAttendance,
  showActions = true,
  filters: externalFilters
}) => {
  const { state } = useAuth();
  const { profile } = state;
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<SessionFilters>({
    instructor_id: '',
    date_from: '',
    date_to: '',
    session_type: '',
    ...externalFilters
  });
  const [showFilters, setShowFilters] = useState(false);

  const sessionTypes = ['training', 'grading', 'competition', 'seminar', 'private'];

  const loadSessions = async () => {
    setLoading(true);
    setError(null);

    try {
      let result;
      
      // Role-based data fetching
      if (profile?.role === 'instructor') {
        result = await sessionService.getInstructorSessions(profile.id);
      } else if (profile?.role === 'admin') {
        result = await sessionService.getSessions(filters);
      } else {
        // Students and guardians see upcoming sessions
        result = await sessionService.getUpcomingSessions(10);
      }

      if (result.error) {
        setError(result.error);
      } else {
        setSessions(result.data);
      }
    } catch (err) {
      setError('Failed to load sessions');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSessions();
  }, [filters, profile]);

  const handleFilterChange = (key: keyof SessionFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleDeleteSession = async (session: Session) => {
    if (!confirm(`Are you sure you want to delete the session "${session.name}"?`)) {
      return;
    }

    const result = await sessionService.deleteSession(session.id);
    if (result.error) {
      setError(result.error);
    } else {
      loadSessions(); // Reload the list
    }
  };

  const formatTime = (time: string) => {
    return new Date(`2000-01-01T${time}`).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getSessionTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      training: 'bg-blue-100 text-blue-800',
      grading: 'bg-purple-100 text-purple-800',
      competition: 'bg-red-100 text-red-800',
      seminar: 'bg-green-100 text-green-800',
      private: 'bg-yellow-100 text-yellow-800'
    };
    return colorMap[type] || 'bg-gray-100 text-gray-800';
  };

  const isUpcoming = (date: string) => {
    return new Date(date) >= new Date();
  };

  const isPast = (date: string) => {
    return new Date(date) < new Date();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-2">
          <Calendar className="h-6 w-6 text-indigo-600" />
          <h2 className="text-2xl font-bold text-gray-900">Sessions</h2>
          <span className="bg-indigo-100 text-indigo-800 text-sm font-medium px-2.5 py-0.5 rounded-full">
            {sessions.length}
          </span>
        </div>
        
        {showActions && (profile?.role === 'instructor' || profile?.role === 'admin') && onCreateSession && (
          <button
            onClick={onCreateSession}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Schedule Session
          </button>
        )}
      </div>

      {/* Filters */}
      {(profile?.role === 'instructor' || profile?.role === 'admin') && (
        <div className="space-y-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </button>

          {showFilters && (
            <div className="bg-gray-50 p-4 rounded-lg space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    From Date
                  </label>
                  <input
                    type="date"
                    value={filters.date_from || ''}
                    onChange={(e) => handleFilterChange('date_from', e.target.value || undefined)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    To Date
                  </label>
                  <input
                    type="date"
                    value={filters.date_to || ''}
                    onChange={(e) => handleFilterChange('date_to', e.target.value || undefined)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Session Type
                  </label>
                  <select
                    value={filters.session_type || ''}
                    onChange={(e) => handleFilterChange('session_type', e.target.value || undefined)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option value="">All Types</option>
                    {sessionTypes.map(type => (
                      <option key={type} value={type}>
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-600">{error}</div>
        </div>
      )}

      {/* Sessions List */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        {sessions.length === 0 ? (
          <div className="text-center py-12">
            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No sessions found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by scheduling a new session.
            </p>
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {sessions.map((session) => (
              <li key={session.id} className={`px-6 py-4 hover:bg-gray-50 ${isPast(session.date) ? 'opacity-75' : ''}`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className={`h-10 w-10 rounded-full flex items-center justify-center ${
                        isUpcoming(session.date) ? 'bg-indigo-100' : 'bg-gray-100'
                      }`}>
                        <Calendar className={`h-5 w-5 ${
                          isUpcoming(session.date) ? 'text-indigo-600' : 'text-gray-600'
                        }`} />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {session.name}
                        </p>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSessionTypeColor(session.session_type || 'training')}`}>
                          {(session.session_type || 'training').charAt(0).toUpperCase() + (session.session_type || 'training').slice(1)}
                        </span>
                        {isPast(session.date) && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Past
                          </span>
                        )}
                      </div>
                      <div className="flex items-center space-x-4 mt-1">
                        <div className="flex items-center text-sm text-gray-500">
                          <Calendar className="h-4 w-4 mr-1" />
                          {new Date(session.date).toLocaleDateString()}
                        </div>
                        <div className="flex items-center text-sm text-gray-500">
                          <Clock className="h-4 w-4 mr-1" />
                          {formatTime(session.start_time)} - {formatTime(session.end_time)}
                        </div>
                        {session.instructor && (
                          <div className="flex items-center text-sm text-gray-500">
                            <Users className="h-4 w-4 mr-1" />
                            {session.instructor.first_name} {session.instructor.last_name}
                          </div>
                        )}
                        <div className="flex items-center text-sm text-gray-500">
                          <Users className="h-4 w-4 mr-1" />
                          {Array.isArray(session.attendance) ? session.attendance.length : 0}/{session.max_capacity || 20}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {showActions && (
                    <div className="flex items-center space-x-2">
                      {onSelectSession && (
                        <button
                          onClick={() => onSelectSession(session)}
                          className="text-indigo-600 hover:text-indigo-900 p-1"
                          title="View Details"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                      )}
                      {onTrackAttendance && (profile?.role === 'instructor' || profile?.role === 'admin') && (
                        <button
                          onClick={() => onTrackAttendance(session)}
                          className="text-green-600 hover:text-green-900 p-1"
                          title="Track Attendance"
                        >
                          <CheckSquare className="h-4 w-4" />
                        </button>
                      )}
                      {onEditSession && (profile?.role === 'instructor' || profile?.role === 'admin') && (
                        <button
                          onClick={() => onEditSession(session)}
                          className="text-gray-600 hover:text-gray-900 p-1"
                          title="Edit Session"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                      )}
                      {(profile?.role === 'instructor' || profile?.role === 'admin') && (
                        <button
                          onClick={() => handleDeleteSession(session)}
                          className="text-red-600 hover:text-red-900 p-1"
                          title="Delete Session"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};
