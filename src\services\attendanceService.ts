import { supabase } from './supabaseClient';
import type { 
  Attendance, 
  RecordAttendanceForm, 
  ApiResponse, 
  PaginatedResponse, 
  AttendanceFilters 
} from '../types';

export const attendanceService = {
  // Get attendance records with optional filtering
  async getAttendance(filters?: AttendanceFilters): Promise<PaginatedResponse<Attendance>> {
    try {
      let query = supabase
        .from('attendance')
        .select(`
          *,
          session:session_id(id, name, date, start_time, end_time),
          student:student_id(id, first_name, last_name, belt_color),
          recorder:recorded_by(id, first_name, last_name, role)
        `)
        .order('recorded_at', { ascending: false });

      // Apply filters
      if (filters?.session_id) {
        query = query.eq('session_id', filters.session_id);
      }
      if (filters?.student_id) {
        query = query.eq('student_id', filters.student_id);
      }
      if (filters?.status) {
        query = query.eq('status', filters.status);
      }
      if (filters?.date_from) {
        query = query.gte('sessions.date', filters.date_from);
      }
      if (filters?.date_to) {
        query = query.lte('sessions.date', filters.date_to);
      }

      const { data, error, count } = await query;

      if (error) {
        return { data: [], count: 0, error: error.message };
      }

      return { data: data || [], count: count || 0, error: null };
    } catch (error) {
      return { data: [], count: 0, error: 'Failed to fetch attendance records' };
    }
  },

  // Get attendance for a specific session
  async getSessionAttendance(sessionId: string): Promise<PaginatedResponse<Attendance>> {
    return this.getAttendance({ session_id: sessionId });
  },

  // Get attendance for a specific student
  async getStudentAttendance(studentId: string): Promise<PaginatedResponse<Attendance>> {
    return this.getAttendance({ student_id: studentId });
  },

  // Record attendance for a student in a session
  async recordAttendance(attendanceData: RecordAttendanceForm, recordedBy: string): Promise<ApiResponse<Attendance>> {
    try {
      const { data, error } = await supabase
        .from('attendance')
        .upsert([{
          ...attendanceData,
          recorded_by: recordedBy
        }], {
          onConflict: 'session_id,student_id'
        })
        .select(`
          *,
          session:session_id(id, name, date, start_time, end_time),
          student:student_id(id, first_name, last_name, belt_color),
          recorder:recorded_by(id, first_name, last_name, role)
        `)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to record attendance' };
    }
  },

  // Bulk record attendance for multiple students
  async recordBulkAttendance(
    sessionId: string, 
    studentAttendance: Array<{ student_id: string; status: 'attended_paid' | 'attended_unpaid' | 'absent' }>,
    recordedBy: string
  ): Promise<ApiResponse<Attendance[]>> {
    try {
      const attendanceRecords = studentAttendance.map(record => ({
        session_id: sessionId,
        student_id: record.student_id,
        status: record.status,
        recorded_by: recordedBy
      }));

      const { data, error } = await supabase
        .from('attendance')
        .upsert(attendanceRecords, {
          onConflict: 'session_id,student_id'
        })
        .select(`
          *,
          session:session_id(id, name, date, start_time, end_time),
          student:student_id(id, first_name, last_name, belt_color),
          recorder:recorded_by(id, first_name, last_name, role)
        `);

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      return { data: null, error: 'Failed to record bulk attendance' };
    }
  },

  // Update attendance status
  async updateAttendance(id: string, status: 'attended_paid' | 'attended_unpaid' | 'absent'): Promise<ApiResponse<Attendance>> {
    try {
      const { data, error } = await supabase
        .from('attendance')
        .update({ status })
        .eq('id', id)
        .select(`
          *,
          session:session_id(id, name, date, start_time, end_time),
          student:student_id(id, first_name, last_name, belt_color),
          recorder:recorded_by(id, first_name, last_name, role)
        `)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to update attendance' };
    }
  },

  // Delete attendance record
  async deleteAttendance(id: string): Promise<ApiResponse<boolean>> {
    try {
      const { error } = await supabase
        .from('attendance')
        .delete()
        .eq('id', id);

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: true, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to delete attendance record' };
    }
  },

  // Get attendance statistics for a student
  async getStudentAttendanceStats(studentId: string, dateFrom?: string, dateTo?: string): Promise<ApiResponse<{
    totalSessions: number;
    attendedPaid: number;
    attendedUnpaid: number;
    absent: number;
    attendanceRate: number;
  }>> {
    try {
      let query = supabase
        .from('attendance')
        .select(`
          status,
          session:sessions!attendance_session_id_fkey(date)
        `)
        .eq('student_id', studentId);

      if (dateFrom) {
        query = query.gte('sessions.date', dateFrom);
      }
      if (dateTo) {
        query = query.lte('sessions.date', dateTo);
      }

      const { data, error } = await query;

      if (error) {
        return { data: null, error: error.message };
      }

      const stats = {
        totalSessions: data?.length || 0,
        attendedPaid: data?.filter(a => a.status === 'attended_paid').length || 0,
        attendedUnpaid: data?.filter(a => a.status === 'attended_unpaid').length || 0,
        absent: data?.filter(a => a.status === 'absent').length || 0,
        attendanceRate: 0
      };

      const totalAttended = stats.attendedPaid + stats.attendedUnpaid;
      stats.attendanceRate = stats.totalSessions > 0 ? (totalAttended / stats.totalSessions) * 100 : 0;

      return { data: stats, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to get attendance statistics' };
    }
  },

  // Get session attendance summary
  async getSessionAttendanceSummary(sessionId: string): Promise<ApiResponse<{
    totalStudents: number;
    attendedPaid: number;
    attendedUnpaid: number;
    absent: number;
    notRecorded: number;
  }>> {
    try {
      const { data, error } = await supabase
        .from('attendance')
        .select('status')
        .eq('session_id', sessionId);

      if (error) {
        return { data: null, error: error.message };
      }

      const summary = {
        totalStudents: data?.length || 0,
        attendedPaid: data?.filter(a => a.status === 'attended_paid').length || 0,
        attendedUnpaid: data?.filter(a => a.status === 'attended_unpaid').length || 0,
        absent: data?.filter(a => a.status === 'absent').length || 0,
        notRecorded: 0 // This would need additional logic to calculate
      };

      return { data: summary, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to get session attendance summary' };
    }
  }
};
