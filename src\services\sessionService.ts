import { supabase } from './supabaseClient';
import type { 
  Session, 
  CreateSessionForm, 
  ApiResponse, 
  PaginatedResponse, 
  SessionFilters 
} from '../types';

export const sessionService = {
  // Get all sessions with optional filtering
  async getSessions(filters?: SessionFilters): Promise<PaginatedResponse<Session>> {
    try {
      let query = supabase
        .from('sessions')
        .select(`
          *,
          instructor:instructor_id(id, first_name, last_name, role),
          attendance_count:attendance(count)
        `)
        .order('date', { ascending: false })
        .order('start_time', { ascending: true });

      // Apply filters
      if (filters?.instructor_id) {
        query = query.eq('instructor_id', filters.instructor_id);
      }
      if (filters?.date_from) {
        query = query.gte('date', filters.date_from);
      }
      if (filters?.date_to) {
        query = query.lte('date', filters.date_to);
      }
      if (filters?.session_type) {
        query = query.eq('session_type', filters.session_type);
      }

      const { data, error, count } = await query;

      if (error) {
        return { data: [], count: 0, error: error.message };
      }

      return { data: data || [], count: count || 0, error: null };
    } catch (error) {
      return { data: [], count: 0, error: 'Failed to fetch sessions' };
    }
  },

  // Get a single session by ID
  async getSession(id: string): Promise<ApiResponse<Session>> {
    try {
      const { data, error } = await supabase
        .from('sessions')
        .select(`
          *,
          instructor:instructor_id(id, first_name, last_name, role),
          attendance_count:attendance(count)
        `)
        .eq('id', id)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to fetch session' };
    }
  },

  // Create a new session
  async createSession(sessionData: CreateSessionForm): Promise<ApiResponse<Session>> {
    try {
      const { data, error } = await supabase
        .from('sessions')
        .insert([sessionData])
        .select(`
          *,
          instructor:instructor_id(id, first_name, last_name, role),
          attendance_count:attendance(count)
        `)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to create session' };
    }
  },

  // Update a session
  async updateSession(id: string, updates: Partial<CreateSessionForm>): Promise<ApiResponse<Session>> {
    try {
      const { data, error } = await supabase
        .from('sessions')
        .update(updates)
        .eq('id', id)
        .select(`
          *,
          instructor:instructor_id(id, first_name, last_name, role),
          attendance_count:attendance(count)
        `)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to update session' };
    }
  },

  // Delete a session
  async deleteSession(id: string): Promise<ApiResponse<boolean>> {
    try {
      const { error } = await supabase
        .from('sessions')
        .delete()
        .eq('id', id);

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: true, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to delete session' };
    }
  },

  // Get sessions for a specific instructor
  async getInstructorSessions(instructorId: string): Promise<PaginatedResponse<Session>> {
    return this.getSessions({ instructor_id: instructorId });
  },

  // Get upcoming sessions
  async getUpcomingSessions(limit?: number): Promise<PaginatedResponse<Session>> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      let query = supabase
        .from('sessions')
        .select(`
          *,
          instructor:instructor_id(id, first_name, last_name, role),
          attendance_count:attendance(count)
        `)
        .gte('date', today)
        .order('date', { ascending: true })
        .order('start_time', { ascending: true });

      if (limit) {
        query = query.limit(limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return { data: [], count: 0, error: error.message };
      }

      return { data: data || [], count: count || 0, error: null };
    } catch (error) {
      return { data: [], count: 0, error: 'Failed to fetch upcoming sessions' };
    }
  },

  // Get sessions for today
  async getTodaySessions(): Promise<PaginatedResponse<Session>> {
    const today = new Date().toISOString().split('T')[0];
    return this.getSessions({ date_from: today, date_to: today });
  },

  // Get sessions for a date range
  async getSessionsInRange(startDate: string, endDate: string): Promise<PaginatedResponse<Session>> {
    return this.getSessions({ date_from: startDate, date_to: endDate });
  },

  // Check if session has capacity for more students
  async checkSessionCapacity(sessionId: string): Promise<ApiResponse<{ hasCapacity: boolean; currentCount: number; maxCapacity: number }>> {
    try {
      // Get session details
      const { data: session, error: sessionError } = await supabase
        .from('sessions')
        .select('max_capacity')
        .eq('id', sessionId)
        .single();

      if (sessionError) {
        return { data: null, error: sessionError.message };
      }

      // Get current attendance count
      const { count: currentCount, error: countError } = await supabase
        .from('attendance')
        .select('*', { count: 'exact', head: true })
        .eq('session_id', sessionId)
        .in('status', ['attended_paid', 'attended_unpaid']);

      if (countError) {
        return { data: null, error: countError.message };
      }

      const maxCapacity = session.max_capacity || 20; // Default capacity
      const hasCapacity = (currentCount || 0) < maxCapacity;

      return { 
        data: { 
          hasCapacity, 
          currentCount: currentCount || 0, 
          maxCapacity 
        }, 
        error: null 
      };
    } catch (error) {
      return { data: null, error: 'Failed to check session capacity' };
    }
  }
};
