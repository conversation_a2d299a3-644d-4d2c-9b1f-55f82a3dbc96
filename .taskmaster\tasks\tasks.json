{"master": {"tasks": [{"id": 1, "title": "Setup Project Repository and Development Environment", "description": "Initialize the project repository with React 18+, TypeScript, and configure the development environment with all required dependencies.", "details": "1. Create a new React project using Vite or Create React App with TypeScript template\n2. Install and configure dependencies:\n   - Tailwind CSS\n   - React Router v6\n   - React Hook Form + Zod\n   - date-fns\n   - Lucide React\n3. Set up ESLint and Prettier for code quality\n4. Configure folder structure:\n   ```\n   src/\n     components/\n     contexts/\n     hooks/\n     pages/\n     services/\n     types/\n     utils/\n   ```\n5. Create initial README.md with project setup instructions\n6. Set up Git repository and initial commit", "testStrategy": "Verify all dependencies are correctly installed and the application builds without errors. Test the development server starts correctly and renders a basic component.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": [{"id": 1, "title": "Initialize React Project with TypeScript", "description": "Create a new React project using Vite or Create React App with TypeScript template", "dependencies": [], "details": "Use Vite for faster development experience: `npm create vite@latest my-project --template react-ts` or alternatively use Create React App: `npx create-react-app my-project --template typescript`. After initialization, review the generated files and ensure TypeScript configuration is properly set up in tsconfig.json.", "status": "pending", "testStrategy": "Verify the project builds successfully with `npm run build` and runs with `npm run dev` or `npm start`. Check that TypeScript compilation works without errors."}, {"id": 2, "title": "Install and Configure Dependencies", "description": "Install and set up all required dependencies including Tailwind CSS, React Router, form libraries, and utility packages", "dependencies": [1], "details": "Install dependencies with: `npm install react-router-dom@6 react-hook-form zod @hookform/resolvers date-fns lucide-react`. For Tailwind CSS, follow the official installation guide: install tailwindcss, postcss, and autoprefixer, then initialize the tailwind.config.js and update the CSS entry file with the Tailwind directives.", "status": "pending", "testStrategy": "Import and use a simple component from each library in App.tsx to verify they're correctly installed. Check that Tailwind classes are properly applied to elements."}, {"id": 3, "title": "Set up <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>", "description": "Configure ESLint and <PERSON><PERSON><PERSON> for consistent code formatting and quality standards", "dependencies": [1], "details": "Install dev dependencies: `npm install -D eslint prettier eslint-config-prettier eslint-plugin-react eslint-plugin-react-hooks @typescript-eslint/eslint-plugin @typescript-eslint/parser`. Create configuration files (.eslintrc.js, .prettierrc) with appropriate rules for React and TypeScript. Add lint scripts to package.json: `\"lint\": \"eslint src --ext .ts,.tsx\"` and `\"format\": \"prettier --write src/**/*.{ts,tsx}\"`.`", "status": "pending", "testStrategy": "Run `npm run lint` and `npm run format` to verify the configuration works. Intentionally introduce a formatting error and confirm it's detected."}, {"id": 4, "title": "Create Project Folder Structure", "description": "Set up the recommended folder structure for organizing components, contexts, hooks, pages, services, types, and utilities", "dependencies": [1], "details": "Create the following directory structure: src/components/ (for reusable UI components), src/contexts/ (for React context providers), src/hooks/ (for custom React hooks), src/pages/ (for route components), src/services/ (for API calls and external services), src/types/ (for TypeScript interfaces and types), src/utils/ (for utility functions). Add index.ts files in each directory to facilitate imports. Create a basic component in components/ to test the structure.", "status": "pending", "testStrategy": "Import the test component in App.tsx to verify the import paths work correctly. Check that TypeScript recognizes the folder structure without path issues."}, {"id": 5, "title": "Initialize Git Repository with README", "description": "Create README.md with setup instructions and initialize Git repository with initial commit", "dependencies": [1, 2, 3, 4], "details": "Create a comprehensive README.md including: project overview, installation instructions, available scripts, folder structure explanation, and technology stack. Initialize Git with `git init`, create .gitignore file (include node_modules/, dist/, .env files), stage all files with `git add .`, and make the initial commit with `git commit -m \"Initial project setup\"`. Optionally, create a remote repository on GitHub/GitLab and push the initial commit.", "status": "pending", "testStrategy": "Verify git status shows a clean working directory. Clone the repository to a new location to ensure it contains all necessary files and can be set up following the README instructions."}]}, {"id": 2, "title": "Configure Supabase Project and Database Schema", "description": "Set up Supabase project, create database tables according to the schema, and implement row-level security policies.", "details": "1. Create a new Supabase project\n2. Set up database tables as per schema:\n   - profiles\n   - students\n   - account_link_requests\n   - sessions\n   - attendance\n   - syllabus\n   - gradings\n   - exercises\n   - fitness_records\n3. Configure relationships between tables\n4. Set up row-level security policies for each table\n5. <PERSON><PERSON> triggers for updated_at columns\n6. Configure storage buckets for avatars\n7. Store Supabase URL and anon key in environment variables\n\nExample RLS policy for profiles:\n```sql\nCREATE POLICY \"Users can view their own profile\"\nON profiles FOR SELECT\nUSING (auth.uid() = id);\n\nCREATE POLICY \"Users can update their own profile\"\nON profiles FOR UPDATE\nUSING (auth.uid() = id);\n```", "testStrategy": "Verify all tables are created correctly with proper relationships. Test RLS policies by attempting to access data with different user roles. Ensure triggers work correctly by testing updated_at functionality.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": [{"id": 1, "title": "Create Supabase Project and Configure Environment", "description": "Set up a new Supabase project and configure the necessary environment variables for the application.", "dependencies": [], "details": "1. Create a new Supabase project through the Supabase dashboard\n2. Note the project URL and anon key\n3. Create a .env.local file in the project root\n4. Add NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY to the environment variables\n5. Configure CORS settings if needed", "status": "pending", "testStrategy": "Verify environment variables are correctly set and the application can connect to Supabase by testing authentication functionality."}, {"id": 2, "title": "Create Database Tables and Relationships", "description": "Create all required database tables according to the schema and establish relationships between them.", "dependencies": [1], "details": "1. Create the following tables: profiles, students, account_link_requests, sessions, attendance, syllabus, gradings, exercises, fitness_records\n2. Define primary keys for each table\n3. Set up foreign key relationships between tables\n4. Configure cascade delete where appropriate\n5. Add indexes for frequently queried columns", "status": "pending", "testStrategy": "Run SQL queries to verify table creation and relationships. Test inserting and querying related data across tables."}, {"id": 3, "title": "Implement Row-Level Security Policies", "description": "Set up row-level security policies for each table to ensure proper data access control.", "dependencies": [2], "details": "1. Create RLS policies for profiles table (view/update own profile)\n2. Create RLS policies for students table (instructors can view/edit, students can view own)\n3. Create RLS policies for sessions, attendance, and syllabus tables\n4. Create RLS policies for gradings, exercises, and fitness_records tables\n5. Create RLS policies for account_link_requests table\n6. Enable Row Level Security on all tables", "status": "pending", "testStrategy": "Test access to tables with different user roles to verify policies are working correctly. Ensure users can only access data they should have permission to view or modify."}, {"id": 4, "title": "Set Up Database Triggers and Functions", "description": "Create triggers for updated_at columns and any other required database functions.", "dependencies": [2], "details": "1. Create a function to automatically update the updated_at timestamp\n2. Add triggers to all tables to call this function when rows are updated\n3. Create any additional helper functions needed for data manipulation\n4. Set up notification triggers if real-time updates are required\n5. Test all triggers and functions", "status": "pending", "testStrategy": "Update records and verify that updated_at timestamps are automatically updated. Test any other database functions to ensure they work as expected."}, {"id": 5, "title": "Configure Storage Buckets and Finalize Setup", "description": "Set up storage buckets for avatars and other media, and finalize the Supabase configuration.", "dependencies": [3, 4], "details": "1. Create a 'avatars' storage bucket in Supabase\n2. Configure public/private access policies for the storage bucket\n3. Set up RLS policies for the storage bucket\n4. Test uploading and retrieving files from the bucket\n5. Document the complete database schema and configuration for the team", "status": "pending", "testStrategy": "Upload test files to the storage bucket and verify they can be retrieved according to the access policies. Ensure the entire Supabase setup works with the application."}]}, {"id": 3, "title": "Implement Authentication System", "description": "Set up Supabase authentication with sign up, sign in, and sign out functionality, including email verification.", "details": "1. Create AuthContext using React Context API and useReducer\n2. Implement authentication service with Supabase client:\n   ```typescript\n   export const supabase = createClient(\n     import.meta.env.VITE_SUPABASE_URL,\n     import.meta.env.VITE_SUPABASE_ANON_KEY\n   );\n   \n   export const signUp = async (email: string, password: string) => {\n     return await supabase.auth.signUp({ email, password });\n   };\n   \n   export const signIn = async (email: string, password: string) => {\n     return await supabase.auth.signInWithPassword({ email, password });\n   };\n   \n   export const signOut = async () => {\n     return await supabase.auth.signOut();\n   };\n   ```\n3. Create sign up, sign in, and sign out UI components with React Hook Form + Zod validation\n4. Implement protected routes with React Router v6\n5. Add email verification flow\n6. Handle authentication errors and display appropriate messages", "testStrategy": "Test sign up, sign in, and sign out functionality with valid and invalid credentials. Verify email verification flow works correctly. Test protected routes redirect unauthenticated users appropriately.", "priority": "high", "dependencies": [1, 2], "status": "pending", "subtasks": [{"id": 1, "title": "Implement AuthContext with React Context API", "description": "Create a global authentication context using React Context API and useReducer to manage authentication state across the application.", "dependencies": [], "details": "Create AuthContext.tsx with: 1) Context definition 2) AuthProvider component 3) useReducer for state management 4) Initial state with user, loading, and error properties 5) Actions for login, logout, signup, and session persistence 6) Custom hook useAuth() for consuming the context", "status": "pending", "testStrategy": "Write unit tests using React Testing Library to verify context initialization, state updates on actions, and proper context consumption through the useAuth hook."}, {"id": 2, "title": "Complete Authentication Service Implementation", "description": "Extend the existing Supabase authentication service with additional functionality for session management, password reset, and email verification.", "dependencies": [], "details": "Add to the existing auth service: 1) getCurrentUser() function 2) resetPassword() function 3) updatePassword() function 4) verifyEmail() function 5) onAuthStateChange() listener setup 6) persistSession() function 7) Error handling utilities", "status": "pending", "testStrategy": "Create unit tests for each authentication function using mock responses from Supabase, testing both success and error scenarios."}, {"id": 3, "title": "Build Authentication UI Components", "description": "Create React components for sign up, sign in, sign out, password reset, and email verification using React Hook Form with Zod validation.", "dependencies": [1, 2], "details": "Develop: 1) SignUpForm component with email/password fields and validation 2) SignInForm component with remember me option 3) ForgotPasswordForm component 4) ResetPasswordForm component 5) EmailVerificationPage component 6) Implement form validation using Zod schemas 7) Add loading states and error handling", "status": "pending", "testStrategy": "Test form submissions, validation behavior, error displays, and integration with the AuthContext using React Testing Library."}, {"id": 4, "title": "Implement Protected Routes with React Router", "description": "Set up route protection using React Router v6 to restrict access to authenticated routes and handle redirects appropriately.", "dependencies": [1], "details": "Create: 1) ProtectedRoute component that checks authentication state 2) PublicOnlyRoute component for routes like login/signup 3) Configure React Router with protected and public routes 4) Add redirect logic for authenticated users trying to access public routes 5) Implement loading states during authentication checks", "status": "pending", "testStrategy": "Test route protection by simulating authenticated and unauthenticated user scenarios, verifying correct redirects and component rendering."}, {"id": 5, "title": "Implement Email Verification Flow", "description": "Create the complete email verification flow including sending verification emails, handling verification links, and updating UI based on verification status.", "dependencies": [2, 3], "details": "Implement: 1) Email verification link handling in a dedicated route 2) Verification status checking on login 3) UI for prompting unverified users to verify email 4) Resend verification email functionality 5) Success/error pages for verification process 6) Update AuthContext to include verification status", "status": "pending", "testStrategy": "Test the verification flow by mocking Supabase responses for verification endpoints and testing UI updates based on verification status changes."}]}, {"id": 4, "title": "Implement User Profile Management", "description": "Create profile management functionality allowing users to view and update their profile information including personal details and avatar.", "details": "1. Create ProfileContext to manage profile state\n2. Implement profile service with Supabase client:\n   ```typescript\n   export const getProfile = async (userId: string) => {\n     return await supabase\n       .from('profiles')\n       .select('*')\n       .eq('id', userId)\n       .single();\n   };\n   \n   export const updateProfile = async (userId: string, profile: Partial<Profile>) => {\n     return await supabase\n       .from('profiles')\n       .update(profile)\n       .eq('id', userId);\n   };\n   ```\n3. Create profile form component with React Hook Form + Zod\n4. Implement avatar upload using Supabase storage\n5. Add form validation for all profile fields\n6. Create profile view and edit pages\n7. Handle different roles (admin, instructor, student, guardian) in the UI", "testStrategy": "Test profile retrieval and update functionality. Verify avatar upload works correctly. Test form validation with valid and invalid inputs. Ensure different roles see appropriate UI elements.", "priority": "high", "dependencies": [3], "status": "pending", "subtasks": []}, {"id": 5, "title": "Implement Role-Based Authorization", "description": "Set up role-based authorization system to control access to features based on user roles (admin, instructor, student, guardian).", "details": "1. Create RoleContext to manage user roles\n2. Implement role-based route protection:\n   ```typescript\n   const ProtectedRoute = ({ children, allowedRoles }: {\n     children: ReactNode;\n     allowedRoles: Role[];\n   }) => {\n     const { user } = useAuth();\n     const { profile } = useProfile();\n     \n     if (!user) return <Navigate to=\"/login\" />;\n     if (!profile) return <div>Loading...</div>;\n     if (!allowedRoles.includes(profile.role)) {\n       return <Navigate to=\"/unauthorized\" />;\n     }\n     \n     return <>{children}</>;\n   };\n   ```\n3. Create role-specific UI components and navigation\n4. Implement role-based feature flags\n5. Set up unauthorized page for users attempting to access restricted features", "testStrategy": "Test route protection with different user roles. Verify UI components display correctly based on role. Test unauthorized redirects work as expected.", "priority": "high", "dependencies": [3, 4], "status": "pending", "subtasks": []}, {"id": 6, "title": "Create Layout and Navigation Components", "description": "Develop responsive layout with sidebar/header navigation and implement protected routes based on user roles.", "details": "1. Create responsive layout components using Tailwind CSS:\n   - MainLayout (container for all pages)\n   - Sidebar (navigation menu)\n   - Header (user info, logout)\n   - Footer\n2. Implement navigation using React Router v6\n3. Create role-based navigation items\n4. Add Lucide icons for navigation items\n5. Implement mobile-responsive design with collapsible sidebar\n6. Create loading and error states for layout components", "testStrategy": "Test responsive layout on different screen sizes. Verify navigation works correctly and displays appropriate items based on user role. Test mobile navigation functionality.", "priority": "medium", "dependencies": [4, 5], "status": "pending", "subtasks": []}, {"id": 7, "title": "Implement Student Management CRUD", "description": "Create functionality for creating, reading, updating, and deleting student records, including all required fields and real-time updates.", "details": "1. Create student service with Supabase client:\n   ```typescript\n   export const getStudents = async () => {\n     return await supabase\n       .from('students')\n       .select('*');\n   };\n   \n   export const getStudent = async (id: string) => {\n     return await supabase\n       .from('students')\n       .select('*')\n       .eq('id', id)\n       .single();\n   };\n   \n   export const createStudent = async (student: Omit<Student, 'id' | 'created_at' | 'updated_at'>) => {\n     return await supabase\n       .from('students')\n       .insert(student);\n   };\n   \n   export const updateStudent = async (id: string, student: Partial<Student>) => {\n     return await supabase\n       .from('students')\n       .update(student)\n       .eq('id', id);\n   };\n   \n   export const deleteStudent = async (id: string) => {\n     return await supabase\n       .from('students')\n       .delete()\n       .eq('id', id);\n   };\n   ```\n2. Create student form component with React Hook Form + Zod\n3. Implement student list with search and filtering\n4. Create student detail view\n5. Add real-time subscription for student data updates\n6. Implement emergency contacts, medical conditions, and guardian linking fields\n7. Add belt color selection and display", "testStrategy": "Test all CRUD operations for student records. Verify form validation works correctly. Test real-time updates when student data changes. Ensure search and filtering functionality works as expected.", "priority": "high", "dependencies": [2, 5, 6], "status": "pending", "subtasks": []}, {"id": 8, "title": "Implement Account Linking System", "description": "Create functionality for users to search for students, submit link requests, and for instructors to approve/reject these requests.", "details": "1. Create account linking service with Supabase client:\n   ```typescript\n   export const searchStudents = async (name: string, dob: string) => {\n     return await supabase\n       .from('students')\n       .select('*')\n       .ilike('first_name', `%${name}%`)\n       .eq('date_of_birth', dob);\n   };\n   \n   export const createLinkRequest = async (request: Omit<AccountLinkRequest, 'id' | 'created_at' | 'updated_at' | 'status'>) => {\n     return await supabase\n       .from('account_link_requests')\n       .insert({ ...request, status: 'pending' });\n   };\n   \n   export const getLinkRequests = async () => {\n     return await supabase\n       .from('account_link_requests')\n       .select('*, students(*), profiles(*)');\n   };\n   \n   export const updateLinkRequestStatus = async (id: string, status: 'approved' | 'rejected') => {\n     return await supabase\n       .from('account_link_requests')\n       .update({ status })\n       .eq('id', id);\n   };\n   ```\n2. Create student search component by name + DOB\n3. Implement link request submission form\n4. Create instructor review interface for pending requests\n5. Implement approval/rejection workflow\n6. Add logic to update permissions on approval\n7. Create notifications for request status changes", "testStrategy": "Test student search functionality with various inputs. Verify link request submission and approval/rejection workflow. Test permission updates after approval. Ensure notifications work correctly.", "priority": "high", "dependencies": [4, 7], "status": "pending", "subtasks": []}, {"id": 9, "title": "Implement Session Management", "description": "Create functionality for instructors to create and manage training sessions with details like name, date, time, instructor, capacity, and type.", "details": "1. Create session service with Supabase client:\n   ```typescript\n   export const getSessions = async () => {\n     return await supabase\n       .from('sessions')\n       .select('*, profiles(first_name, last_name)');\n   };\n   \n   export const getSession = async (id: string) => {\n     return await supabase\n       .from('sessions')\n       .select('*, profiles(first_name, last_name)')\n       .eq('id', id)\n       .single();\n   };\n   \n   export const createSession = async (session: Omit<Session, 'id' | 'created_at' | 'updated_at'>) => {\n     return await supabase\n       .from('sessions')\n       .insert(session);\n   };\n   \n   export const updateSession = async (id: string, session: Partial<Session>) => {\n     return await supabase\n       .from('sessions')\n       .update(session)\n       .eq('id', id);\n   };\n   \n   export const deleteSession = async (id: string) => {\n     return await supabase\n       .from('sessions')\n       .delete()\n       .eq('id', id);\n   };\n   ```\n2. Create session form component with React Hook Form + Zod\n3. Implement session calendar view using date-fns\n4. Create session list with filtering by date, instructor, type\n5. Add session detail view\n6. Implement capacity management and validation", "testStrategy": "Test all CRUD operations for sessions. Verify form validation works correctly. Test calendar view displays sessions accurately. Ensure filtering functionality works as expected. Test capacity validation.", "priority": "medium", "dependencies": [5, 6], "status": "pending", "subtasks": []}, {"id": 10, "title": "Implement Attendance Tracking", "description": "Create functionality for instructors to mark and track student attendance for sessions with different attendance statuses.", "details": "1. Create attendance service with Supabase client:\n   ```typescript\n   export const getAttendanceForSession = async (sessionId: string) => {\n     return await supabase\n       .from('attendance')\n       .select('*, students(*)')\n       .eq('session_id', sessionId);\n   };\n   \n   export const getAttendanceForStudent = async (studentId: string) => {\n     return await supabase\n       .from('attendance')\n       .select('*, sessions(*)')\n       .eq('student_id', studentId);\n   };\n   \n   export const markAttendance = async (attendance: Omit<Attendance, 'id' | 'created_at' | 'updated_at'>) => {\n     return await supabase\n       .from('attendance')\n       .upsert(attendance, { onConflict: 'session_id,student_id' });\n   };\n   ```\n2. Create attendance marking interface for instructors\n3. Implement attendance status options (attended_paid, attended_unpaid, absent)\n4. Create attendance reports by student and session\n5. Add validation to ensure one attendance record per student per session\n6. Implement role-based access control for attendance marking (instructors only)", "testStrategy": "Test attendance marking functionality with different statuses. Verify one attendance record per student per session constraint. Test attendance reports display correctly. Ensure only instructors can mark attendance.", "priority": "medium", "dependencies": [7, 9], "status": "pending", "subtasks": []}, {"id": 11, "title": "Implement Syllabus Management", "description": "Create functionality to manage syllabus items grouped by belt color, category, and skill for the martial arts curriculum.", "details": "1. Create syllabus service with Supabase client:\n   ```typescript\n   export const getSyllabusItems = async () => {\n     return await supabase\n       .from('syllabus')\n       .select('*');\n   };\n   \n   export const getSyllabusItemsByBelt = async (beltColor: string) => {\n     return await supabase\n       .from('syllabus')\n       .select('*')\n       .eq('belt_color', beltColor);\n   };\n   \n   export const createSyllabusItem = async (item: Omit<SyllabusItem, 'id' | 'created_at' | 'updated_at'>) => {\n     return await supabase\n       .from('syllabus')\n       .insert(item);\n   };\n   \n   export const updateSyllabusItem = async (id: string, item: Partial<SyllabusItem>) => {\n     return await supabase\n       .from('syllabus')\n       .update(item)\n       .eq('id', id);\n   };\n   \n   export const deleteSyllabusItem = async (id: string) => {\n     return await supabase\n       .from('syllabus')\n       .delete()\n       .eq('id', id);\n   };\n   ```\n2. Create syllabus item form component with React Hook Form + Zod\n3. Implement syllabus view grouped by belt color, category, and skill\n4. Create syllabus management interface for instructors/admins\n5. Add validation for syllabus item fields", "testStrategy": "Test all CRUD operations for syllabus items. Verify grouping by belt color, category, and skill works correctly. Test form validation. Ensure only instructors/admins can manage syllabus items.", "priority": "medium", "dependencies": [2, 6], "status": "pending", "subtasks": []}, {"id": 12, "title": "Implement Grading System", "description": "Create functionality for instructors to record and track student gradings for syllabus items with different grading results.", "details": "1. Create grading service with Supabase client:\n   ```typescript\n   export const getGradingsForStudent = async (studentId: string) => {\n     return await supabase\n       .from('gradings')\n       .select('*, syllabus(*)')\n       .eq('student_id', studentId);\n   };\n   \n   export const getGradingsForSyllabusItem = async (syllabusId: string) => {\n     return await supabase\n       .from('gradings')\n       .select('*, students(*)')\n       .eq('syllabus_id', syllabusId);\n   };\n   \n   export const recordGrading = async (grading: Omit<Grading, 'id' | 'created_at' | 'updated_at'>) => {\n     return await supabase\n       .from('gradings')\n       .upsert(grading, { onConflict: 'student_id,syllabus_id' });\n   };\n   ```\n2. Create grading interface for instructors\n3. Implement grading result options (pass, fail, not_assessed)\n4. Create student grading reports by belt color\n5. Add validation to ensure one grading record per student-skill\n6. Implement role-based access control for grading (instructors only)", "testStrategy": "Test grading recording functionality with different results. Verify one grading record per student-skill constraint. Test grading reports display correctly. Ensure only instructors can record gradings.", "priority": "medium", "dependencies": [7, 11], "status": "pending", "subtasks": []}, {"id": 13, "title": "Implement Exercise Definition System", "description": "Create functionality to define exercises with different measurement types (count, time, weight, distance) for fitness tracking.", "details": "1. Create exercise service with Supabase client:\n   ```typescript\n   export const getExercises = async () => {\n     return await supabase\n       .from('exercises')\n       .select('*');\n   };\n   \n   export const getExercise = async (id: string) => {\n     return await supabase\n       .from('exercises')\n       .select('*')\n       .eq('id', id)\n       .single();\n   };\n   \n   export const createExercise = async (exercise: Omit<Exercise, 'id' | 'created_at' | 'updated_at'>) => {\n     return await supabase\n       .from('exercises')\n       .insert(exercise);\n   };\n   \n   export const updateExercise = async (id: string, exercise: Partial<Exercise>) => {\n     return await supabase\n       .from('exercises')\n       .update(exercise)\n       .eq('id', id);\n   };\n   \n   export const deleteExercise = async (id: string) => {\n     return await supabase\n       .from('exercises')\n       .delete()\n       .eq('id', id);\n   };\n   ```\n2. Create exercise form component with React Hook Form + Zod\n3. Implement exercise list with filtering by type\n4. Create exercise detail view\n5. Add validation for exercise fields\n6. Implement measurement type options (count, time, weight, distance)", "testStrategy": "Test all CRUD operations for exercises. Verify form validation works correctly. Test filtering functionality. Ensure measurement type options work as expected.", "priority": "low", "dependencies": [2, 6], "status": "pending", "subtasks": []}, {"id": 14, "title": "Implement Fitness Tracking System", "description": "Create functionality for students and instructors to record and track fitness results for defined exercises.", "details": "1. Create fitness record service with Supabase client:\n   ```typescript\n   export const getFitnessRecordsForStudent = async (studentId: string) => {\n     return await supabase\n       .from('fitness_records')\n       .select('*, exercises(*)')\n       .eq('student_id', studentId);\n   };\n   \n   export const getFitnessRecordsForExercise = async (exerciseId: string) => {\n     return await supabase\n       .from('fitness_records')\n       .select('*, students(*)')\n       .eq('exercise_id', exerciseId);\n   };\n   \n   export const recordFitness = async (record: Omit<FitnessRecord, 'id' | 'created_at' | 'updated_at'>) => {\n     return await supabase\n       .from('fitness_records')\n       .insert(record);\n   };\n   ```\n2. Create fitness recording interface for students and instructors\n3. Implement verification system for instructor-verified records\n4. Create fitness progress tracking charts\n5. Implement leaderboards for exercises\n6. Add validation for fitness record fields based on exercise measurement type", "testStrategy": "Test fitness recording functionality for both students and instructors. Verify verification system works correctly. Test progress tracking charts display accurately. Ensure leaderboards show correct rankings.", "priority": "low", "dependencies": [7, 13], "status": "pending", "subtasks": []}, {"id": 15, "title": "Implement Real-time Subscriptions", "description": "Set up real-time subscriptions for critical data updates using Supabase's real-time features.", "details": "1. Implement real-time subscription for student data:\n   ```typescript\n   const subscribeToStudents = () => {\n     const subscription = supabase\n       .channel('public:students')\n       .on('postgres_changes', {\n         event: '*',\n         schema: 'public',\n         table: 'students'\n       }, (payload) => {\n         // Handle real-time update\n       })\n       .subscribe();\n     \n     return () => {\n       supabase.removeChannel(subscription);\n     };\n   };\n   ```\n2. Create real-time subscription for account link requests\n3. Implement real-time updates for attendance\n4. Add real-time updates for gradings\n5. Create real-time subscription for fitness records\n6. Implement optimistic UI updates for better user experience", "testStrategy": "Test real-time updates for all subscribed tables. Verify UI updates correctly when data changes. Test subscription cleanup on component unmount. Ensure optimistic UI updates work correctly.", "priority": "medium", "dependencies": [7, 8, 10, 12, 14], "status": "pending", "subtasks": []}, {"id": 16, "title": "Implement Error Handling and Notifications", "description": "Create a comprehensive error handling system and user notifications for important actions and events.", "details": "1. Create error handling context:\n   ```typescript\n   export const ErrorContext = createContext<{\n     error: Error | null;\n     setError: (error: Error | null) => void;\n   }>({ error: null, setError: () => {} });\n   \n   export const ErrorProvider = ({ children }: { children: ReactNode }) => {\n     const [error, setError] = useState<Error | null>(null);\n     \n     return (\n       <ErrorContext.Provider value={{ error, setError }}>\n         {error && <ErrorAlert error={error} onClose={() => setError(null)} />}\n         {children}\n       </ErrorContext.Provider>\n     );\n   };\n   ```\n2. Implement notification system for success/error messages\n3. Create error boundary components for graceful failure handling\n4. Add form error handling and display\n5. Implement API error handling and retry logic\n6. Create loading states and indicators", "testStrategy": "Test error handling with various error scenarios. Verify notifications display correctly for different actions. Test error boundaries catch and display errors properly. Ensure loading states display appropriately.", "priority": "medium", "dependencies": [3, 4, 7, 8, 10, 12, 14], "status": "pending", "subtasks": []}, {"id": 17, "title": "Implement Data Export and Reporting", "description": "Create functionality to export data and generate reports for students, attendance, gradings, and fitness records.", "details": "1. Create export service for data download:\n   ```typescript\n   export const exportStudentData = async () => {\n     const { data, error } = await supabase\n       .from('students')\n       .select('*');\n     \n     if (error) throw error;\n     \n     // Convert to CSV or Excel format\n     const csv = convertToCSV(data);\n     \n     // Create download link\n     const blob = new Blob([csv], { type: 'text/csv' });\n     const url = URL.createObjectURL(blob);\n     const link = document.createElement('a');\n     link.href = url;\n     link.download = 'students.csv';\n     link.click();\n   };\n   ```\n2. Implement attendance reports by date range\n3. Create grading progress reports by belt color\n4. Implement fitness progress reports\n5. Add data visualization for key metrics\n6. Create printable report formats", "testStrategy": "Test data export functionality for different data types. Verify reports display correct information. Test data visualization components. Ensure printable reports format correctly.", "priority": "low", "dependencies": [7, 10, 12, 14], "status": "pending", "subtasks": []}, {"id": 18, "title": "Implement Form Validation with <PERSON>od", "description": "Set up comprehensive form validation using Zod schemas for all forms in the application.", "details": "1. Create Zod schemas for all data types:\n   ```typescript\n   export const profileSchema = z.object({\n     first_name: z.string().min(1, 'First name is required'),\n     last_name: z.string().min(1, 'Last name is required'),\n     date_of_birth: z.string().regex(/^\\d{4}-\\d{2}-\\d{2}$/, 'Invalid date format (YYYY-MM-DD)'),\n     phone: z.string().optional(),\n     address: addressSchema.optional(),\n     avatar_url: z.string().url().optional(),\n     role: z.enum(['admin', 'instructor', 'student', 'guardian'])\n   });\n   \n   export const studentSchema = z.object({\n     first_name: z.string().min(1, 'First name is required'),\n     last_name: z.string().min(1, 'Last name is required'),\n     date_of_birth: z.string().regex(/^\\d{4}-\\d{2}-\\d{2}$/, 'Invalid date format (YYYY-MM-DD)'),\n     belt_color: z.string().min(1, 'Belt color is required'),\n     emergency_contacts: z.array(emergencyContactSchema).min(1, 'At least one emergency contact is required'),\n     medical_conditions: z.array(medicalConditionSchema).optional(),\n     guardian_id: z.string().uuid().optional()\n   });\n   ```\n2. Integrate Zod schemas with React Hook Form for all forms\n3. Implement client-side validation for all forms\n4. Add server-side validation for all API endpoints\n5. Create reusable form components with built-in validation\n6. Implement error message display for form fields", "testStrategy": "Test form validation with valid and invalid inputs for all forms. Verify error messages display correctly. Test server-side validation works as expected. Ensure reusable form components handle validation correctly.", "priority": "high", "dependencies": [3, 4, 7, 8, 9, 11, 13], "status": "pending", "subtasks": []}, {"id": 19, "title": "Optimize Performance and Implement Caching", "description": "Optimize application performance and implement caching strategies for frequently accessed data.", "details": "1. Implement React.memo for performance-critical components\n2. Add useMemo and useCallback hooks for expensive calculations and callbacks\n3. Implement client-side caching for frequently accessed data:\n   ```typescript\n   export const useStudents = () => {\n     const [students, setStudents] = useState<Student[]>([]);\n     const [loading, setLoading] = useState(true);\n     const [error, setError] = useState<Error | null>(null);\n     \n     useEffect(() => {\n       const fetchStudents = async () => {\n         try {\n           setLoading(true);\n           const { data, error } = await getStudents();\n           if (error) throw error;\n           setStudents(data || []);\n         } catch (err) {\n           setError(err as Error);\n         } finally {\n           setLoading(false);\n         }\n       };\n       \n       fetchStudents();\n       \n       // Set up real-time subscription\n       const subscription = supabase\n         .channel('public:students')\n         .on('postgres_changes', {\n           event: '*',\n           schema: 'public',\n           table: 'students'\n         }, (payload) => {\n           // Update cache based on change type\n           if (payload.eventType === 'INSERT') {\n             setStudents(prev => [...prev, payload.new as Student]);\n           } else if (payload.eventType === 'UPDATE') {\n             setStudents(prev => prev.map(s => s.id === payload.new.id ? payload.new as Student : s));\n           } else if (payload.eventType === 'DELETE') {\n             setStudents(prev => prev.filter(s => s.id !== payload.old.id));\n           }\n         })\n         .subscribe();\n       \n       return () => {\n         supabase.removeChannel(subscription);\n       };\n     }, []);\n     \n     return { students, loading, error };\n   };\n   ```\n4. Implement pagination for large data sets\n5. Add lazy loading for components and routes\n6. Optimize database queries with proper indexes", "testStrategy": "Test application performance with large data sets. Verify caching works correctly and updates when data changes. Test pagination with different page sizes. Ensure lazy loading improves initial load time.", "priority": "low", "dependencies": [7, 9, 11, 13, 15], "status": "pending", "subtasks": []}, {"id": 20, "title": "Implement End-to-End Testing and Deployment Pipeline", "description": "Set up comprehensive end-to-end testing and create a deployment pipeline for the application.", "details": "1. Set up Cypress for end-to-end testing\n2. Create test cases for critical user flows:\n   - Authentication\n   - Student management\n   - Account linking\n   - Attendance tracking\n   - Grading\n   - Fitness tracking\n3. Implement CI/CD pipeline with GitHub Actions or similar tool\n4. Set up staging and production environments\n5. Configure environment variables for different environments\n6. Create deployment scripts and documentation\n7. Implement database migration strategy\n8. Set up monitoring and error tracking", "testStrategy": "Run end-to-end tests for all critical user flows. Verify CI/CD pipeline deploys correctly to staging and production. Test environment-specific configurations. Ensure monitoring and error tracking work as expected.", "priority": "medium", "dependencies": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-06-17T10:49:39.956Z", "updated": "2025-06-17T10:49:39.956Z", "description": "Tasks for master context"}}}