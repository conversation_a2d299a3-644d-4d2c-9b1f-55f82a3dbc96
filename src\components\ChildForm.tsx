import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Calendar, User, Heart, Trash2, Plus } from 'lucide-react';
import { MedicalConditionsSelector } from './MedicalConditionsSelector';
import { ConsentModal } from './ConsentModal';

export interface ChildData {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  medicalConditions: Array<{
    condition_id: string;
    condition_name: string;
    notes?: string;
    severity?: string;
    medication?: string;
  }>;
  consentSocialMedia: boolean;
  consentAssumptionRisk: boolean;
}

interface ChildFormProps {
  children: ChildData[];
  onChange: (children: ChildData[]) => void;
}

interface SingleChildFormData {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
}

export const ChildForm: React.FC<ChildFormProps> = ({ children, onChange }) => {
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [showConsentModal, setShowConsentModal] = useState<'social' | 'risk' | null>(null);
  const [consentModalChild, setConsentModalChild] = useState<number | null>(null);
  const [socialMediaConsentsRead, setSocialMediaConsentsRead] = useState<Set<number>>(new Set());
  const [riskConsentsRead, setRiskConsentsRead] = useState<Set<number>>(new Set());

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<SingleChildFormData>();

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return age - 1;
    }
    return age;
  };

  const addChild = (data: SingleChildFormData) => {
    const newChild: ChildData = {
      ...data,
      medicalConditions: [],
      consentSocialMedia: false,
      consentAssumptionRisk: false
    };

    const updatedChildren = [...children, newChild];
    onChange(updatedChildren);
    reset();
  };

  const removeChild = (index: number) => {
    const updatedChildren = children.filter((_, i) => i !== index);
    onChange(updatedChildren);
    
    // Clean up consent tracking
    const newSocialConsents = new Set(socialMediaConsentsRead);
    const newRiskConsents = new Set(riskConsentsRead);
    newSocialConsents.delete(index);
    newRiskConsents.delete(index);
    setSocialMediaConsentsRead(newSocialConsents);
    setRiskConsentsRead(newRiskConsents);
  };

  const updateChildMedicalConditions = (index: number, medicalConditions: ChildData['medicalConditions']) => {
    const updatedChildren = children.map((child, i) => 
      i === index ? { ...child, medicalConditions } : child
    );
    onChange(updatedChildren);
  };

  const openConsentModal = (type: 'social' | 'risk', childIndex: number) => {
    setShowConsentModal(type);
    setConsentModalChild(childIndex);
  };

  const handleConsentRead = (type: 'social' | 'risk', childIndex: number) => {
    if (type === 'social') {
      setSocialMediaConsentsRead(prev => new Set([...prev, childIndex]));
    } else {
      setRiskConsentsRead(prev => new Set([...prev, childIndex]));
    }
    setShowConsentModal(null);
    setConsentModalChild(null);
  };

  const updateChildConsent = (index: number, field: 'consentSocialMedia' | 'consentAssumptionRisk', value: boolean) => {
    const updatedChildren = children.map((child, i) => 
      i === index ? { ...child, [field]: value } : child
    );
    onChange(updatedChildren);
  };

  return (
    <div className="space-y-6">
      {/* Add Child Form */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Plus className="h-5 w-5 mr-2 text-indigo-600" />
          Add Child
        </h3>
        
        <form onSubmit={handleSubmit(addChild)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                First Name *
              </label>
              <input
                type="text"
                {...register('firstName', { required: 'First name is required' })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              {errors.firstName && (
                <p className="mt-1 text-sm text-red-600">{errors.firstName.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Last Name *
              </label>
              <input
                type="text"
                {...register('lastName', { required: 'Last name is required' })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              {errors.lastName && (
                <p className="mt-1 text-sm text-red-600">{errors.lastName.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date of Birth *
              </label>
              <input
                type="date"
                {...register('dateOfBirth', { required: 'Date of birth is required' })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              {errors.dateOfBirth && (
                <p className="mt-1 text-sm text-red-600">{errors.dateOfBirth.message}</p>
              )}
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Child
            </button>
          </div>
        </form>
      </div>

      {/* Children List */}
      {children.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Children ({children.length})</h3>
          
          {children.map((child, index) => (
            <div key={index} className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                    <User className="h-5 w-5 text-indigo-600" />
                  </div>
                  <div>
                    <h4 className="text-lg font-medium text-gray-900">
                      {child.firstName} {child.lastName}
                    </h4>
                    <p className="text-sm text-gray-500 flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {new Date(child.dateOfBirth).toLocaleDateString()} 
                      (Age: {calculateAge(child.dateOfBirth)})
                    </p>
                  </div>
                </div>
                
                <button
                  onClick={() => removeChild(index)}
                  className="text-red-600 hover:text-red-800 p-1"
                  title="Remove child"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>

              {/* Medical Conditions */}
              <div className="mb-6">
                <h5 className="text-md font-medium text-gray-900 mb-3 flex items-center">
                  <Heart className="h-4 w-4 mr-2 text-red-500" />
                  Medical Conditions
                </h5>
                <MedicalConditionsSelector
                  selectedConditions={child.medicalConditions}
                  onChange={(conditions) => updateChildMedicalConditions(index, conditions)}
                />
              </div>

              {/* Consent Checkboxes */}
              <div className="space-y-4">
                <h5 className="text-md font-medium text-gray-900">Consent & Permissions</h5>
                
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <input
                      type="checkbox"
                      id={`social-${index}`}
                      checked={child.consentSocialMedia}
                      disabled={!socialMediaConsentsRead.has(index)}
                      onChange={(e) => updateChildConsent(index, 'consentSocialMedia', e.target.checked)}
                      className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded disabled:opacity-50"
                    />
                    <div className="flex-1">
                      <label htmlFor={`social-${index}`} className="text-sm text-gray-900">
                        I consent to social media usage (photos/videos for school promotion)
                      </label>
                      <button
                        type="button"
                        onClick={() => openConsentModal('social', index)}
                        className="block text-xs text-indigo-600 hover:text-indigo-800 underline mt-1"
                      >
                        {socialMediaConsentsRead.has(index) ? 'Review policy' : 'Read policy to enable consent'}
                      </button>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <input
                      type="checkbox"
                      id={`risk-${index}`}
                      checked={child.consentAssumptionRisk}
                      disabled={!riskConsentsRead.has(index)}
                      onChange={(e) => updateChildConsent(index, 'consentAssumptionRisk', e.target.checked)}
                      className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded disabled:opacity-50"
                    />
                    <div className="flex-1">
                      <label htmlFor={`risk-${index}`} className="text-sm text-gray-900">
                        I acknowledge the assumption of risk for martial arts training
                      </label>
                      <button
                        type="button"
                        onClick={() => openConsentModal('risk', index)}
                        className="block text-xs text-indigo-600 hover:text-indigo-800 underline mt-1"
                      >
                        {riskConsentsRead.has(index) ? 'Review policy' : 'Read policy to enable consent'}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Consent Modal */}
      {showConsentModal && consentModalChild !== null && (
        <ConsentModal
          type={showConsentModal}
          onClose={() => {
            setShowConsentModal(null);
            setConsentModalChild(null);
          }}
          onRead={() => handleConsentRead(showConsentModal, consentModalChild)}
        />
      )}
    </div>
  );
};
