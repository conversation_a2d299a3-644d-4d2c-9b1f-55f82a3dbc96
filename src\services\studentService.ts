import { supabase } from './supabaseClient';
import type { 
  Student, 
  CreateStudentForm, 
  ApiResponse, 
  PaginatedResponse, 
  StudentFilters 
} from '../types';

export const studentService = {
  // Get all students with optional filtering
  async getStudents(filters?: StudentFilters): Promise<PaginatedResponse<Student>> {
    try {
      let query = supabase
        .from('students')
        .select(`
          *,
          guardian:guardian_id(id, first_name, last_name, role)
        `)
        .order('last_name', { ascending: true });

      // Apply filters
      if (filters?.belt_color) {
        query = query.eq('belt_color', filters.belt_color);
      }
      if (filters?.active !== undefined) {
        query = query.eq('active', filters.active);
      }
      if (filters?.guardian_id) {
        query = query.eq('guardian_id', filters.guardian_id);
      }
      if (filters?.search) {
        query = query.or(`first_name.ilike.%${filters.search}%,last_name.ilike.%${filters.search}%`);
      }

      const { data, error, count } = await query;

      if (error) {
        return { data: [], count: 0, error: error.message };
      }

      return { data: data || [], count: count || 0, error: null };
    } catch (error) {
      return { data: [], count: 0, error: 'Failed to fetch students' };
    }
  },

  // Get a single student by ID
  async getStudent(id: string): Promise<ApiResponse<Student>> {
    try {
      const { data, error } = await supabase
        .from('students')
        .select(`
          *,
          guardian:guardian_id(id, first_name, last_name, role)
        `)
        .eq('id', id)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to fetch student' };
    }
  },

  // Create a new student
  async createStudent(studentData: CreateStudentForm): Promise<ApiResponse<Student>> {
    try {
      const { data, error } = await supabase
        .from('students')
        .insert([studentData])
        .select(`
          *,
          guardian:guardian_id(id, first_name, last_name, role)
        `)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to create student' };
    }
  },

  // Update a student
  async updateStudent(id: string, updates: Partial<CreateStudentForm>): Promise<ApiResponse<Student>> {
    try {
      const { data, error } = await supabase
        .from('students')
        .update(updates)
        .eq('id', id)
        .select(`
          *,
          guardian:guardian_id(id, first_name, last_name, role)
        `)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to update student' };
    }
  },

  // Delete a student (soft delete by setting active to false)
  async deleteStudent(id: string): Promise<ApiResponse<boolean>> {
    try {
      const { error } = await supabase
        .from('students')
        .update({ active: false })
        .eq('id', id);

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: true, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to delete student' };
    }
  },

  // Get students by guardian ID
  async getStudentsByGuardian(guardianId: string): Promise<PaginatedResponse<Student>> {
    return this.getStudents({ guardian_id: guardianId });
  },

  // Get students linked to a user account
  async getStudentsByUser(userId: string): Promise<PaginatedResponse<Student>> {
    try {
      const { data, error, count } = await supabase
        .from('students')
        .select(`
          *,
          guardian:guardian_id(id, first_name, last_name, role)
        `)
        .or(`user_id.eq.${userId},guardian_id.eq.${userId}`)
        .eq('active', true)
        .order('last_name', { ascending: true });

      if (error) {
        return { data: [], count: 0, error: error.message };
      }

      return { data: data || [], count: count || 0, error: null };
    } catch (error) {
      return { data: [], count: 0, error: 'Failed to fetch user students' };
    }
  },

  // Update student belt color after grading
  async updateBeltColor(studentId: string, newBeltColor: string): Promise<ApiResponse<Student>> {
    try {
      const { data, error } = await supabase
        .from('students')
        .update({
          belt_color: newBeltColor,
          last_graded: new Date().toISOString().split('T')[0] // Today's date
        })
        .eq('id', studentId)
        .select(`
          *,
          guardian:guardian_id(id, first_name, last_name, role)
        `)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to update belt color' };
    }
  }
};
