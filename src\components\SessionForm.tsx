import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Save, X, Calendar, Clock, Users, User } from 'lucide-react';
import { sessionService } from '../services';
import type { Session, CreateSessionForm, Profile } from '../types';
import { useAuth } from '../contexts/AuthContext';

interface SessionFormProps {
  session?: Session;
  onSave: (session: Session) => void;
  onCancel: () => void;
}

export const SessionForm: React.FC<SessionFormProps> = ({
  session,
  onSave,
  onCancel
}) => {
  const { state } = useAuth();
  const { profile } = state;
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [instructors, setInstructors] = useState<Profile[]>([]);

  const sessionTypes = ['training', 'grading', 'competition', 'seminar', 'private'];

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<CreateSessionForm>({
    defaultValues: session ? {
      name: session.name,
      date: session.date,
      start_time: session.start_time,
      end_time: session.end_time,
      instructor_id: session.instructor_id,
      max_capacity: session.max_capacity || 20,
      session_type: session.session_type || 'training'
    } : {
      name: '',
      date: '',
      start_time: '',
      end_time: '',
      instructor_id: profile?.role === 'instructor' ? profile.id : '',
      max_capacity: 20,
      session_type: 'training'
    }
  });

  // Load instructors for selection (admin only)
  useEffect(() => {
    const loadInstructors = async () => {
      // For now, we'll just use the current user if they're an instructor
      if (profile?.role === 'instructor') {
        setInstructors([profile]);
      } else if (profile?.role === 'admin') {
        // This would need a service to fetch users with instructor role
        // For now, we'll leave it empty and require manual entry
        setInstructors([]);
      }
    };
    loadInstructors();
  }, [profile]);

  const onSubmit = async (data: CreateSessionForm) => {
    setLoading(true);
    setError(null);

    try {
      // Ensure instructor_id is set properly
      const sessionData = {
        ...data,
        instructor_id: data.instructor_id || profile?.id || '',
      };

      // Validate required fields
      if (!sessionData.instructor_id) {
        setError('Instructor is required');
        setLoading(false);
        return;
      }

      let result;
      if (session) {
        result = await sessionService.updateSession(session.id, sessionData);
      } else {
        result = await sessionService.createSession(sessionData);
      }

      if (result.error) {
        setError(result.error);
      } else if (result.data) {
        onSave(result.data);
      }
    } catch (err) {
      setError('Failed to save session');
    } finally {
      setLoading(false);
    }
  };

  const generateTimeOptions = () => {
    const options = [];
    for (let hour = 6; hour <= 22; hour++) {
      for (let minute = 0; minute < 60; minute += 15) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        const displayTime = new Date(`2000-01-01T${timeString}`).toLocaleTimeString([], { 
          hour: '2-digit', 
          minute: '2-digit' 
        });
        options.push({ value: timeString, label: displayTime });
      }
    }
    return options;
  };

  const timeOptions = generateTimeOptions();

  // Auto-generate session name based on type and date
  const sessionType = watch('session_type');
  const sessionDate = watch('date');
  
  useEffect(() => {
    if (sessionType && sessionDate && !session) {
      const date = new Date(sessionDate);
      const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
      const formattedDate = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      const suggestedName = `${sessionType.charAt(0).toUpperCase() + sessionType.slice(1)} - ${dayName} ${formattedDate}`;
      setValue('name', suggestedName);
    }
  }, [sessionType, sessionDate, session, setValue]);

  return (
    <div className="max-w-2xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
      <div className="px-6 py-4 bg-indigo-600 text-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Calendar className="h-6 w-6" />
            <h2 className="text-xl font-semibold">
              {session ? 'Edit Session' : 'Schedule New Session'}
            </h2>
          </div>
          <button
            onClick={onCancel}
            className="text-indigo-200 hover:text-white"
          >
            <X className="h-6 w-6" />
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="text-sm text-red-600">{error}</div>
          </div>
        )}

        {/* Session Details */}
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Calendar className="h-5 w-5 mr-2 text-indigo-600" />
            Session Details
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Session Name *
              </label>
              <input
                type="text"
                {...register('name', { required: 'Session name is required' })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="e.g., Training - Monday Evening"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Session Type *
              </label>
              <select
                {...register('session_type', { required: 'Session type is required' })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                {sessionTypes.map(type => (
                  <option key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </option>
                ))}
              </select>
              {errors.session_type && (
                <p className="mt-1 text-sm text-red-600">{errors.session_type.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Max Capacity
              </label>
              <div className="relative">
                <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="number"
                  min="1"
                  max="50"
                  {...register('max_capacity', { 
                    valueAsNumber: true,
                    min: { value: 1, message: 'Capacity must be at least 1' },
                    max: { value: 50, message: 'Capacity cannot exceed 50' }
                  })}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
              {errors.max_capacity && (
                <p className="mt-1 text-sm text-red-600">{errors.max_capacity.message}</p>
              )}
            </div>
          </div>
        </div>

        {/* Date and Time */}
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Clock className="h-5 w-5 mr-2 text-indigo-600" />
            Date & Time
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date *
              </label>
              <input
                type="date"
                {...register('date', { required: 'Date is required' })}
                min={new Date().toISOString().split('T')[0]}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              {errors.date && (
                <p className="mt-1 text-sm text-red-600">{errors.date.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Start Time *
              </label>
              <select
                {...register('start_time', { required: 'Start time is required' })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">Select time</option>
                {timeOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.start_time && (
                <p className="mt-1 text-sm text-red-600">{errors.start_time.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                End Time *
              </label>
              <select
                {...register('end_time', { required: 'End time is required' })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">Select time</option>
                {timeOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.end_time && (
                <p className="mt-1 text-sm text-red-600">{errors.end_time.message}</p>
              )}
            </div>
          </div>
        </div>

        {/* Instructor */}
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <User className="h-5 w-5 mr-2 text-indigo-600" />
            Instructor
          </h3>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Instructor *
            </label>
            {instructors.length > 0 ? (
              <select
                {...register('instructor_id', { required: 'Instructor is required' })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">Select instructor</option>
                {instructors.map(instructor => (
                  <option key={instructor.id} value={instructor.id}>
                    {instructor.first_name} {instructor.last_name}
                  </option>
                ))}
              </select>
            ) : (
              <>
                <input
                  type="text"
                  value={profile?.role === 'instructor' ? `${profile.first_name} ${profile.last_name}` : ''}
                  disabled
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500"
                />
                {/* Hidden input to register the instructor_id */}
                <input
                  type="hidden"
                  {...register('instructor_id', { required: 'Instructor is required' })}
                  value={profile?.role === 'instructor' ? profile.id : ''}
                />
              </>
            )}
            {errors.instructor_id && (
              <p className="mt-1 text-sm text-red-600">{errors.instructor_id.message}</p>
            )}
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            {session ? 'Update Session' : 'Schedule Session'}
          </button>
        </div>
      </form>
    </div>
  );
};
