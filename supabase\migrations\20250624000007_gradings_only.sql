-- Add missing columns to gradings table if they don't exist
DO $$
BEGIN
    -- Add status column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'gradings' AND column_name = 'status') THEN
        ALTER TABLE gradings ADD COLUMN status TEXT CHECK (status IN ('not_started', 'in_progress', 'mastered', 'needs_improvement')) DEFAULT 'not_started';
    END IF;

    -- Add instructor_notes column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'gradings' AND column_name = 'instructor_notes') THEN
        ALTER TABLE gradings ADD COLUMN instructor_notes TEXT;
    END IF;

    -- Add graded_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'gradings' AND column_name = 'graded_at') THEN
        ALTER TABLE gradings ADD COLUMN graded_at TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add graded_by column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'gradings' AND column_name = 'graded_by') THEN
        ALTER TABLE gradings ADD COLUMN graded_by UUID REFERENCES profiles(id);
    END IF;
END $$;

-- Create indexes for performance (only if columns exist)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'gradings' AND column_name = 'status') THEN
        CREATE INDEX IF NOT EXISTS idx_gradings_status ON gradings(status);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'gradings' AND column_name = 'graded_at') THEN
        CREATE INDEX IF NOT EXISTS idx_gradings_graded_at ON gradings(graded_at);
    END IF;
END $$;
