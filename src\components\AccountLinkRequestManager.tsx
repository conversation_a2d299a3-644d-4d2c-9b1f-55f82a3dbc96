import React, { useState, useEffect } from 'react';
import { Check, X, Clock, User, MessageSquare, AlertCircle } from 'lucide-react';
import { accountLinkService } from '../services';
import { useAuth } from '../contexts/AuthContext';

interface LinkRequest {
  id: string;
  student_id: string;
  guardian_id: string;
  relationship_type: 'self' | 'guardian';
  status: 'pending' | 'approved' | 'rejected';
  request_message?: string;
  created_at: string;
  student: {
    id: string;
    first_name: string;
    last_name: string;
    belt_color: string;
    date_of_birth: string;
  };
  requester: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    role: string;
  };
}

export const AccountLinkRequestManager: React.FC = () => {
  const { state } = useAuth();
  const { profile } = state;
  const [requests, setRequests] = useState<LinkRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('pending');

  useEffect(() => {
    loadRequests();
  }, []);

  const loadRequests = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await accountLinkService.getAllRequests();
      if (result.error) {
        setError(result.error);
      } else {
        setRequests(result.data);
      }
    } catch (err) {
      setError('Failed to load account link requests');
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (requestId: string) => {
    if (!profile) return;
    
    setProcessing(requestId);
    setError(null);

    try {
      const result = await accountLinkService.approveRequest(requestId, profile.id);
      if (result.error) {
        setError(result.error);
      } else {
        await loadRequests(); // Reload to get updated data
      }
    } catch (err) {
      setError('Failed to approve request');
    } finally {
      setProcessing(null);
    }
  };

  const handleReject = async (requestId: string) => {
    if (!profile) return;
    
    setProcessing(requestId);
    setError(null);

    try {
      const result = await accountLinkService.rejectRequest(requestId, profile.id);
      if (result.error) {
        setError(result.error);
      } else {
        await loadRequests(); // Reload to get updated data
      }
    } catch (err) {
      setError('Failed to reject request');
    } finally {
      setProcessing(null);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <Check className="h-5 w-5 text-green-500" />;
      case 'rejected':
        return <X className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-50 border-green-200';
      case 'rejected':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-yellow-50 border-yellow-200';
    }
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return age - 1;
    }
    return age;
  };

  const filteredRequests = requests.filter(request => {
    if (filter === 'all') return true;
    return request.status === filter;
  });

  const getFilterCounts = () => {
    return {
      all: requests.length,
      pending: requests.filter(r => r.status === 'pending').length,
      approved: requests.filter(r => r.status === 'approved').length,
      rejected: requests.filter(r => r.status === 'rejected').length,
    };
  };

  const counts = getFilterCounts();

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Account Link Requests</h2>
              <p className="text-sm text-gray-600 mt-1">
                Manage guardian and student account linking requests
              </p>
            </div>
            
            {/* Filter Tabs */}
            <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
              {[
                { key: 'pending', label: 'Pending', count: counts.pending },
                { key: 'approved', label: 'Approved', count: counts.approved },
                { key: 'rejected', label: 'Rejected', count: counts.rejected },
                { key: 'all', label: 'All', count: counts.all },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setFilter(tab.key as any)}
                  className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                    filter === tab.key
                      ? 'bg-white text-indigo-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {tab.label} ({tab.count})
                </button>
              ))}
            </div>
          </div>
        </div>

        {error && (
          <div className="px-6 py-4 bg-red-50 border-b border-red-200">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
              <span className="text-sm text-red-600">{error}</span>
            </div>
          </div>
        )}

        <div className="divide-y divide-gray-200">
          {filteredRequests.length === 0 ? (
            <div className="text-center py-12">
              <User className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No requests found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {filter === 'pending' 
                  ? 'No pending account link requests at this time.'
                  : `No ${filter} requests found.`
                }
              </p>
            </div>
          ) : (
            filteredRequests.map((request) => (
              <div
                key={request.id}
                className={`p-6 ${getStatusColor(request.status)}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 space-y-4">
                    {/* Header */}
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(request.status)}
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">
                          {request.relationship_type === 'self' ? 'Student Account Link' : 'Guardian Account Link'}
                        </h3>
                        <p className="text-sm text-gray-500">
                          Requested {new Date(request.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    {/* Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Student Info */}
                      <div className="bg-white rounded-lg p-4 border border-gray-200">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Student</h4>
                        <div className="space-y-1">
                          <p className="text-sm text-gray-900">
                            {request.student.first_name} {request.student.last_name}
                          </p>
                          <p className="text-sm text-gray-500">
                            Age: {calculateAge(request.student.date_of_birth)}
                          </p>
                          <p className="text-sm text-gray-500">
                            Belt: {request.student.belt_color.charAt(0).toUpperCase() + request.student.belt_color.slice(1)}
                          </p>
                        </div>
                      </div>

                      {/* Requester Info */}
                      <div className="bg-white rounded-lg p-4 border border-gray-200">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">
                          {request.relationship_type === 'self' ? 'User' : 'Guardian'}
                        </h4>
                        <div className="space-y-1">
                          <p className="text-sm text-gray-900">
                            {request.requester.first_name} {request.requester.last_name}
                          </p>
                          <p className="text-sm text-gray-500">
                            {request.requester.email}
                          </p>
                          <p className="text-sm text-gray-500">
                            Relationship: {request.relationship_type === 'self' ? 'Self' : 'Parent/Guardian'}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Message */}
                    {request.request_message && (
                      <div className="bg-white rounded-lg p-4 border border-gray-200">
                        <div className="flex items-start space-x-2">
                          <MessageSquare className="h-4 w-4 text-gray-400 mt-0.5" />
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">Message</h4>
                            <p className="text-sm text-gray-600 mt-1">{request.request_message}</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  {request.status === 'pending' && (
                    <div className="flex space-x-2 ml-6">
                      <button
                        onClick={() => handleApprove(request.id)}
                        disabled={processing === request.id}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                      >
                        {processing === request.id ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        ) : (
                          <Check className="h-4 w-4 mr-2" />
                        )}
                        Approve
                      </button>
                      <button
                        onClick={() => handleReject(request.id)}
                        disabled={processing === request.id}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                      >
                        {processing === request.id ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        ) : (
                          <X className="h-4 w-4 mr-2" />
                        )}
                        Reject
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};
