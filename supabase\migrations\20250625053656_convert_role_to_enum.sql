-- Convert 'role' column from TEXT to ENUM
-- Step 1: Create the user_role enum type if it doesn't exist
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('student', 'instructor', 'guardian', 'admin');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Step 2: Drop dependent policies
DROP POLICY IF EXISTS "Users can read own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
DROP POLICY IF EXISTS "Service role can manage all profiles" ON profiles;
DROP POLICY IF EXISTS "Instructors can view all students" ON students;
DROP POLICY IF EXISTS "Instructors can view all requests" ON account_link_requests;

-- Step 3: Remove the CHECK constraint on the role column
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_role_check;

-- Step 4: Rename existing column and create new one
ALTER TABLE profiles RENAME COLUMN role TO role_old;
ALTER TABLE profiles ADD COLUMN role user_role;

-- Step 5: Copy data from old column to new column
UPDATE profiles SET role = role_old::user_role;

-- Step 6: Set NOT NULL constraint and default value
ALTER TABLE profiles ALTER COLUMN role SET NOT NULL;
ALTER TABLE profiles ALTER COLUMN role SET DEFAULT 'student'::user_role;

-- Step 7: Drop the old column
ALTER TABLE profiles DROP COLUMN role_old;

-- Step 8: Recreate policies
CREATE POLICY "Users can read own profile" 
  ON profiles FOR SELECT TO authenticated 
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" 
  ON profiles FOR UPDATE TO authenticated 
  USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" 
  ON profiles FOR INSERT TO authenticated 
  WITH CHECK (auth.uid() = id);

CREATE POLICY "Service role can manage all profiles" 
  ON profiles TO service_role 
  USING (true);

-- Create policies that depend on the role enum
CREATE POLICY "Instructors can view all students" 
  ON students FOR SELECT TO authenticated 
  USING ((SELECT role FROM profiles WHERE profiles.id = auth.uid()) = 'instructor'::user_role OR 
         (SELECT role FROM profiles WHERE profiles.id = auth.uid()) = 'admin'::user_role);

CREATE POLICY "Instructors can view all requests" 
  ON account_link_requests FOR SELECT TO authenticated 
  USING ((SELECT role FROM profiles WHERE profiles.id = auth.uid()) = 'instructor'::user_role OR 
         (SELECT role FROM profiles WHERE profiles.id = auth.uid()) = 'admin'::user_role);

-- Step 9: Create index on role column for better query performance
DROP INDEX IF EXISTS idx_profiles_role;
CREATE INDEX idx_profiles_role ON profiles(role);
