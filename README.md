# Augment Martial Arts CRM

## Setup

1. Install dependencies: `npm install`
2. Start dev server: `npm run dev`

## Tech Stack

- React 18 + TypeScript
- Tailwind CSS
- Supabase
- React Router v6
- React Hook Form + Zod
- date-fns
- Lucide React

## Folder Structure

```
src/
  components/
  contexts/
  hooks/
  pages/
  services/
  types/
  utils/
```

## Features
- Student management, attendance, grading, fitness tracking, account linking
- Role-based access (admin, instructor, student, guardian)
- Real-time updates with Supabase
- Form validation with Zod
- Responsive UI with Tailwind CSS
