import React, { createContext, useContext, useReducer, useEffect } from 'react';
import type { ReactNode } from 'react';
import { supabase } from '../services/supabaseClient';
import { fetchUserProfile } from '../services/authService';

interface Profile {
  id: string;
  first_name: string;
  last_name: string;
  role: string; // Required by schema constraints
  phone?: string;
  avatar_url?: string;
  date_of_birth?: string;
  address?: any;
  created_at?: string;
  updated_at?: string;
}

interface AuthState {
  user: any | null;
  profile: Profile | null;
  loading: boolean;
  error: string | null;
}

type AuthAction =
  | { type: 'LOGIN'; payload: any }
  | { type: 'SET_PROFILE'; payload: Profile }
  | { type: 'LOGOUT' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null };

const initialState: AuthState = {
  user: null,
  profile: null,
  loading: true,
  error: null,
};

function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'LOGIN':
      return { ...state, user: action.payload, loading: false, error: null };
    case 'SET_PROFILE':
      return { ...state, profile: action.payload, loading: false };
    case 'LOGOUT':
      return { ...state, user: null, profile: null, loading: false, error: null };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    default:
      return state;
  }
}

const AuthContext = createContext<{
  state: AuthState;
  dispatch: React.Dispatch<AuthAction>;
} | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);
  // Function to load user profile
  const loadUserProfile = async (userId: string) => {
    try {
      const { data: profile, error } = await fetchUserProfile(userId);
      if (error) {
        console.error('Error loading profile:', error);
        // Don't retry on RLS errors, just continue without profile
        if ((error as any)?.message?.includes('infinite recursion') || (error as any)?.code === '42P17') {
          console.log('RLS error detected, continuing without profile');
          return;
        }
        return;
      }
      if (profile) {
        dispatch({ type: 'SET_PROFILE', payload: profile });
      } else {
        console.log('No profile found for user:', userId);
        // User exists but no profile - this is okay, they might need to complete profile
      }
    } catch (error) {
      console.error('Profile loading error:', error);
    }
  };

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }: { data: { session: any } }) => {
      if (session?.user) {
        dispatch({ type: 'LOGIN', payload: session.user });
        loadUserProfile(session.user.id);
      } else {
        dispatch({ type: 'LOGOUT' });
      }
    });
    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_event: string, session: any) => {
        if (session?.user) {
          dispatch({ type: 'LOGIN', payload: session.user });
          loadUserProfile(session.user.id);
        } else {
          dispatch({ type: 'LOGOUT' });
        }
      }
    );
    return () => subscription.unsubscribe();
  }, []);

  return (
    <AuthContext.Provider value={{ state, dispatch }}>
      {children}
    </AuthContext.Provider>
  );
};

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 